<?php
/**
 * 动态参数模板系统外部API控制器
 * 供迅排设计服务调用
 */

namespace app\api\controller;

use app\common\model\PosterTemplateConfig;
use app\common\model\PosterUserData;
use think\facade\Log;
use think\facade\Validate;
use think\facade\Config;

class PosterExternal extends ApiBase
{
    /**
     * 不需要登录验证的方法
     */
    public $like_not_need_login = ['getParameterData', 'getParameterConfig', 'health', 'batchGetParameterData', 'updatePreviewUrl'];
    
    /**
     * 初始化 - 验证API Key
     */
    public function initialize()
    {
        parent::initialize();
        $this->validateApiKey();
    }
    
    /**
     * 验证API Key
     */
    protected function validateApiKey()
    {
        $apiKey = $this->request->header('Authorization');
        if ($apiKey) {
            $apiKey = str_replace('Bearer ', '', $apiKey);
        }
        
        $configApiKey = Config::get('poster_template.external_api.api_key');
        
        if (empty($apiKey) || $apiKey !== $configApiKey) {
            Log::warning('Invalid API key', [
                'provided_key' => $apiKey ? substr($apiKey, 0, 8) . '...' : 'none',
                'ip' => $this->request->ip(),
                'user_agent' => $this->request->header('User-Agent')
            ]);
            
            $this->_error('Invalid API key', 401);
        }
    }
    
    /**
     * 获取参数数据
     * GET /api/poster-external/parameter-data/{dataId}
     */
    public function getParameterData($dataId = '')
    {
        try {
            if (empty($dataId)) {
                $this->_error('Parameter data ID is required');
            }
            
            // 查询用户数据
            $userData = PosterUserData::where('id', $dataId)
                ->with(['config'])
                ->find();
            
            if (!$userData) {
                Log::warning('Parameter data not found', ['data_id' => $dataId]);
                $this->_error('Parameter data not found');
            }
            
            // 构建响应数据
            $responseData = [
                'id' => $userData->id,
                'configId' => $userData->config_id,
                'templateId' => $userData->config->template_id ?? '',
                'parameterValues' => $userData->parameter_values
            ];
            
            Log::info('Parameter data retrieved', [
                'data_id' => $dataId,
                'config_id' => $userData->config_id
            ]);
            
            $this->_success('success', $responseData);
            
        } catch (\Exception $e) {
            Log::error('Get parameter data error', [
                'data_id' => $dataId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->_error('Internal server error');
        }
    }
    
    /**
     * 获取参数配置
     * GET /api/poster-external/parameter-config/{configId}
     */
    public function getParameterConfig($configId = '')
    {
        try {
            if (empty($configId)) {
                $this->_error('Parameter config ID is required');
            }
            
            // 查询配置数据
            $config = PosterTemplateConfig::where('id', $configId)
                ->where('status', PosterTemplateConfig::STATUS_ENABLED)
                ->find();
            
            if (!$config) {
                Log::warning('Parameter config not found', ['config_id' => $configId]);
                $this->_error('Parameter config not found');
            }
            
            // 构建响应数据
            $responseData = [
                'id' => $config->id,
                'templateId' => $config->template_id,
                'parameters' => $config->parameters
            ];
            
            Log::info('Parameter config retrieved', [
                'config_id' => $configId,
                'template_id' => $config->template_id
            ]);
            
            $this->_success('success', $responseData);
            
        } catch (\Exception $e) {
            Log::error('Get parameter config error', [
                'config_id' => $configId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->_error('Internal server error');
        }
    }
    
    /**
     * 健康检查
     * GET /api/poster-external/health
     */
    public function health()
    {
        try {
            // 检查数据库连接
            $dbStatus = 'ok';
            try {
                PosterTemplateConfig::count();
            } catch (\Exception $e) {
                $dbStatus = 'error';
                Log::error('Database health check failed', ['error' => $e->getMessage()]);
            }
            
            $responseData = [
                'status' => $dbStatus === 'ok' ? 'ok' : 'error',
                'timestamp' => date('c'),
                'services' => [
                    'database' => $dbStatus
                ]
            ];
            
            $this->_success('success', $responseData);
            
        } catch (\Exception $e) {
            Log::error('Health check error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->_error('Health check failed');
        }
    }

    /**
     * 批量获取参数数据
     * POST /api/poster-external/parameter-data/batch
     */
    public function batchGetParameterData()
    {
        try {
            $input = $this->request->post();

            // 验证输入
            $validate = Validate::make([
                'dataIds' => 'require|array',
                'dataIds.*' => 'require|alphaNum'
            ]);

            if (!$validate->check($input)) {
                $this->_error($validate->getError());
            }

            $dataIds = $input['dataIds'];
            if (count($dataIds) > 50) {
                $this->_error('Too many data IDs, maximum 50 allowed');
            }

            // 批量查询
            $userDataList = PosterUserData::whereIn('id', $dataIds)
                ->with(['config'])
                ->select();

            $responseData = [];
            foreach ($userDataList as $userData) {
                $responseData[] = [
                    'id' => $userData->id,
                    'configId' => $userData->config_id,
                    'templateId' => $userData->config->template_id ?? '',
                    'parameterValues' => $userData->parameter_values
                ];
            }

            Log::info('Batch parameter data retrieved', [
                'requested_count' => count($dataIds),
                'found_count' => count($responseData)
            ]);

            $this->_success('success', [
                'items' => $responseData,
                'total' => count($responseData),
                'requested' => count($dataIds)
            ]);

        } catch (\Exception $e) {
            Log::error('Batch get parameter data error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->_error('Internal server error');
        }
    }

    /**
     * 更新用户数据的预览URL
     * PUT /api/poster-external/parameter-data/{dataId}/preview-url
     */
    public function updatePreviewUrl($dataId = '')
    {
        try {
            if (empty($dataId)) {
                $this->_error('Parameter data ID is required');
            }

            $input = $this->request->put();

            // 验证输入
            $validate = Validate::make([
                'previewUrl' => 'require|url'
            ]);

            if (!$validate->check($input)) {
                $this->_error($validate->getError());
            }

            // 更新预览URL
            $result = PosterUserData::where('id', $dataId)->update([
                'preview_url' => $input['previewUrl']
            ]);

            if ($result === false) {
                $this->_error('Parameter data not found');
            }

            Log::info('Preview URL updated', [
                'data_id' => $dataId,
                'preview_url' => $input['previewUrl']
            ]);

            $this->_success('success', [
                'id' => $dataId,
                'previewUrl' => $input['previewUrl'],
                'updatedAt' => date('c')
            ]);

        } catch (\Exception $e) {
            Log::error('Update preview URL error', [
                'data_id' => $dataId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->_error('Internal server error');
        }
    }
}
