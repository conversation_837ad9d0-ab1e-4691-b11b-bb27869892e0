<?php
/**
 * 动态参数模板系统路由配置
 */

use think\facade\Route;

// ============================================================================
// 外部API路由（供迅排设计服务调用）
// ============================================================================

Route::group('api/poster-external', function () {
    // 获取参数数据
    Route::get('parameter-data/:dataId', 'api/PosterExternal/getParameterData');
    
    // 获取参数配置
    Route::get('parameter-config/:configId', 'api/PosterExternal/getParameterConfig');
    
    // 批量获取参数数据
    Route::post('parameter-data/batch', 'api/PosterExternal/batchGetParameterData');
    
    // 更新预览URL
    Route::put('parameter-data/:dataId/preview-url', 'api/PosterExternal/updatePreviewUrl');
    
    // 健康检查
    Route::get('health', 'api/PosterExternal/health');
});

// ============================================================================
// 后台管理路由
// ============================================================================

Route::group('admin/poster_template', function () {
    // 配置管理
    Route::get('config_list', 'admin/PosterTemplate/config_list');
    Route::get('config_add', 'admin/PosterTemplate/config_add');
    Route::post('config_add', 'admin/PosterTemplate/config_add');
    Route::get('config_edit/:id', 'admin/PosterTemplate/config_edit');
    Route::post('config_edit/:id', 'admin/PosterTemplate/config_edit');
    Route::post('config_delete/:id', 'admin/PosterTemplate/config_delete');
    Route::post('config_copy', 'admin/PosterTemplate/config_copy');
    
    // 模板相关
    Route::get('template_browser', 'admin/PosterTemplate/template_browser');
    Route::get('get_templates', 'admin/PosterTemplate/getTemplates');
    Route::post('parse_template', 'admin/PosterTemplate/parseTemplate');
    
    // 用户数据管理
    Route::get('user_data_list', 'admin/PosterTemplate/userDataList');
    
    // 预览和生成
    Route::post('generate_preview', 'admin/PosterTemplate/generatePreview');
    Route::post('generate_image', 'admin/PosterTemplate/generateImage');
    
    // 统计数据
    Route::get('get_stats', 'admin/PosterTemplate/getStats');
});

// ============================================================================
// 用户端路由（供移动端项目调用的API）
// ============================================================================

Route::group('api/poster', function () {
    // 获取配置列表
    Route::get('configs', 'api/Poster/getConfigs');
    
    // 获取配置详情
    Route::get('config/:configId', 'api/Poster/getConfig');
    
    // 保存用户数据
    Route::post('user-data', 'api/Poster/saveUserData');
    
    // 更新用户数据
    Route::put('user-data/:dataId', 'api/Poster/updateUserData');
    
    // 获取用户数据
    Route::get('user-data/:dataId', 'api/Poster/getUserData');
    
    // 获取用户数据列表
    Route::get('user-data', 'api/Poster/getUserDataList');
    
    // 生成预览
    Route::post('preview', 'api/Poster/generatePreview');
    
    // 生成图片
    Route::post('generate', 'api/Poster/generateImage');
    
    // 获取生成记录
    Route::get('records/:dataId', 'api/Poster/getGenerationRecords');
});

// ============================================================================
// 用户端页面路由（如果需要）
// ============================================================================

Route::group('poster', function () {
    // 模板列表页面
    Route::get('/', 'index/PosterTemplate/index');
    
    // 动态表单页面
    Route::get('form/:configId', 'index/PosterTemplate/form');
    
    // 我的作品页面
    Route::get('my-works', 'index/PosterTemplate/myWorks');
    
    // 预览页面
    Route::get('preview/:dataId', 'index/PosterTemplate/preview');
    
    // AJAX接口
    Route::post('save-data', 'index/PosterTemplate/saveData');
    Route::post('generate-preview', 'index/PosterTemplate/generatePreview');
    Route::post('generate-image', 'index/PosterTemplate/generateImage');
});
