<?php
/**
 * 动态参数模板系统 - 模板管理服务
 */

namespace app\common\service;

use app\common\model\PosterTemplateConfig;
use app\common\model\PosterUserData;
use think\facade\Log;
use think\facade\Db;

class PosterTemplateService
{
    protected $apiClient;
    
    public function __construct()
    {
        $this->apiClient = new PosterApiClient();
    }
    
    /**
     * 获取模板列表
     * @param array $params
     * @return array
     */
    public function getTemplateList($params = [])
    {
        try {
            $response = $this->apiClient->getTemplates($params);
            
            if (!$response['success']) {
                throw new \Exception($response['error'] ?? 'Failed to get templates');
            }
            
            return [
                'success' => true,
                'data' => $response['data']['data'] ?? []
            ];
            
        } catch (\Exception $e) {
            Log::error('Get template list failed', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取模板详情
     * @param string $templateId
     * @return array
     */
    public function getTemplateDetail($templateId)
    {
        try {
            $response = $this->apiClient->getTemplate($templateId);
            
            if (!$response['success']) {
                throw new \Exception($response['error'] ?? 'Failed to get template');
            }
            
            return [
                'success' => true,
                'data' => $response['data']['data'] ?? []
            ];
            
        } catch (\Exception $e) {
            Log::error('Get template detail failed', [
                'template_id' => $templateId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 解析模板并创建配置
     * @param string $templateId
     * @param string $configName
     * @param string $configDescription
     * @param string $createdBy
     * @return array
     */
    public function parseAndCreateConfig($templateId, $configName, $configDescription = '', $createdBy = '')
    {
        // 使用数据库事务确保数据一致性
        return Db::transaction(function() use ($templateId, $configName, $configDescription, $createdBy) {
            try {
                Log::info('Starting parseAndCreateConfig', [
                    'template_id' => $templateId,
                    'config_name' => $configName,
                    'created_by' => $createdBy
                ]);

                // 1. 解析模板
                $parseResponse = $this->apiClient->parseTemplate($templateId);

                if (!$parseResponse['success']) {
                    throw new \Exception($parseResponse['error'] ?? 'Failed to parse template');
                }

                // 修复：正确解析API响应数据结构
                $parseData = $parseResponse['data'] ?? [];

                Log::info('Template parsed successfully', [
                    'template_id' => $templateId,
                    'parse_data_keys' => array_keys($parseData),
                    'has_parameter_candidates' => isset($parseData['parameterCandidates']),
                    'parameter_count' => count($parseData['parameterCandidates'] ?? [])
                ]);

                // 2. 准备配置数据
                $configData = [
                    'template_id' => $templateId,
                    'template_title' => $parseData['templateTitle'] ?? $parseData['template_title'] ?? '',
                    'config_name' => $configName,
                    'config_description' => $configDescription,
                    'parameters' => $parseData['parameterCandidates'] ?? [],
                    'created_by' => $createdBy,
                    'status' => PosterTemplateConfig::STATUS_ENABLED
                ];

                Log::info('Prepared config data', [
                    'template_id' => $configData['template_id'],
                    'template_title' => $configData['template_title'],
                    'config_name' => $configData['config_name'],
                    'parameter_count' => count($configData['parameters']),
                    'status' => $configData['status'],
                    'parameters_type' => gettype($configData['parameters'])
                ]);

                // 3. 创建配置记录
                $config = PosterTemplateConfig::createConfig($configData);

                if (!$config) {
                    throw new \Exception('Failed to create template config - createConfig returned false');
                }

                // 4. 立即验证数据是否写入成功
                $verifyConfig = PosterTemplateConfig::where('id', $config->id)->find();
                if (!$verifyConfig) {
                    throw new \Exception('Config created but verification failed - data not found in database');
                }

                Log::info('Template config created and verified', [
                    'config_id' => $config->id,
                    'template_id' => $templateId,
                    'created_by' => $createdBy,
                    'verification_success' => true
                ]);
            
            return [
                'success' => true,
                'data' => [
                    'configId' => $config->id,
                    'templateId' => $templateId,
                    'configName' => $configName,
                    'parameterCandidates' => $parseData['parameterCandidates'] ?? [],
                    'summary' => $parseData['summary'] ?? []
                ]
            ];
            
        } catch (\Exception $e) {
            Log::error('Parse and create config failed', [
                'template_id' => $templateId,
                'config_name' => $configName,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
        });
    }
    
    /**
     * 更新模板配置
     * @param string $configId
     * @param array $updateData
     * @return array
     */
    public function updateConfig($configId, $updateData)
    {
        try {
            // 验证参数结构
            if (isset($updateData['parameters']) && 
                !PosterTemplateConfig::validateParameters($updateData['parameters'])) {
                throw new \Exception('Invalid parameters structure');
            }
            
            $result = PosterTemplateConfig::updateConfig($configId, $updateData);
            
            if (!$result) {
                throw new \Exception('Config not found or update failed');
            }
            
            Log::info('Template config updated', [
                'config_id' => $configId,
                'updated_fields' => array_keys($updateData)
            ]);
            
            return [
                'success' => true,
                'data' => ['configId' => $configId]
            ];
            
        } catch (\Exception $e) {
            Log::error('Update config failed', [
                'config_id' => $configId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取配置列表
     * @param array $filters
     * @param int $page
     * @param int $pageSize
     * @return array
     */
    public function getConfigList($filters = [], $page = 1, $pageSize = 15)
    {
        try {
            // 确保分页参数有效
            $page = max(1, intval($page));
            $pageSize = max(1, intval($pageSize));
            $pageSize = min(100, $pageSize); // 限制最大页面大小


            $where = [];

            // 处理筛选条件
            if (!empty($filters['template_id'])) {
                $where['template_id'] = $filters['template_id'];
            }
            if (!empty($filters['created_by'])) {
                $where['created_by'] = $filters['created_by'];
            }
            if (isset($filters['status'])) {
                $where['status'] = $filters['status'];
            } else {
                // 默认只查询启用的配置
                $where['status'] = PosterTemplateConfig::STATUS_ENABLED;
            }


            
            // 搜索关键词
            $query = PosterTemplateConfig::where($where);
            if (!empty($filters['keyword'])) {
                $keyword = $filters['keyword'];
                $query->where(function($q) use ($keyword) {
                    $q->whereLike('config_name', "%{$keyword}%")
                      ->whereOr('template_title', 'like', "%{$keyword}%")
                      ->whereOr('config_description', 'like', "%{$keyword}%");
                });
            }
            
            // 分页查询
            $total = $query->count();





            $list = $query->order('created_at desc')
                         ->page($page, $pageSize)
                         ->select();





            // 转换为数组并添加基本统计信息
            $listArray = [];
            foreach ($list as $item) {
                try {
                    // 获取原始数据，避免时间戳自动转换问题
                    $rawData = $item->getData();

                    // 处理parameters字段 - 可能已经是数组或者是JSON字符串
                    $parameters = $rawData['parameters'];
                    if (is_string($parameters)) {
                        $parameters = json_decode($parameters, true) ?: [];
                    } elseif (!is_array($parameters)) {
                        $parameters = [];
                    }

                    // 手动构建数组，避免toArray()的时间戳转换问题
                    $itemArray = [
                        'id' => $rawData['id'],
                        'template_id' => $rawData['template_id'],
                        'template_title' => $rawData['template_title'],
                        'config_name' => $rawData['config_name'],
                        'config_description' => $rawData['config_description'],
                        'parameters' => $parameters,
                        'created_by' => $rawData['created_by'],
                        'status' => $rawData['status'],
                        'created_at' => $rawData['created_at'],
                        'updated_at' => $rawData['updated_at']
                    ];

                    $itemArray['user_data_count'] = PosterUserData::where('config_id', $item->id)->count();
                    $listArray[] = $itemArray;

                } catch (\Exception $e) {
                    Log::error('Error processing item: ' . $e->getMessage());
                    Log::error('Item ID: ' . ($item->id ?? 'unknown'));
                    Log::error('Error file: ' . $e->getFile() . ' line: ' . $e->getLine());
                    throw $e;
                }
            }
            
            // 安全的分页计算
            $totalPages = $pageSize > 0 ? ceil($total / $pageSize) : 0;

            // 调试：打印最终返回数据
            $returnData = [
                'list' => $listArray,
                'total' => intval($total),
                'page' => intval($page),
                'pageSize' => intval($pageSize),
                'totalPages' => intval($totalPages)
            ];



            return [
                'success' => true,
                'data' => $returnData
            ];
            
        } catch (\Exception $e) {
            Log::error('Get config list failed', [
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取配置详情
     * @param string $configId
     * @return array
     */
    public function getConfigDetail($configId)
    {
        try {
            $config = PosterTemplateConfig::where('id', $configId)->find();
            
            if (!$config) {
                throw new \Exception('Config not found');
            }
            
            // 添加统计信息
            $config['parameter_stats'] = $config->getParameterStats();
            $config['user_data_stats'] = PosterUserData::getDataStats($configId);
            
            return [
                'success' => true,
                'data' => $config
            ];
            
        } catch (\Exception $e) {
            Log::error('Get config detail failed', [
                'config_id' => $configId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 删除配置
     * @param string $configId
     * @return array
     */
    public function deleteConfig($configId)
    {
        try {
            // 检查是否有关联的用户数据
            $userDataCount = PosterUserData::where('config_id', $configId)->count();
            if ($userDataCount > 0) {
                throw new \Exception("Cannot delete config with {$userDataCount} user data records");
            }
            
            $result = PosterTemplateConfig::deleteConfig($configId);
            
            if (!$result) {
                throw new \Exception('Config not found or delete failed');
            }
            
            Log::info('Template config deleted', ['config_id' => $configId]);
            
            return [
                'success' => true,
                'data' => ['configId' => $configId]
            ];
            
        } catch (\Exception $e) {
            Log::error('Delete config failed', [
                'config_id' => $configId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 复制配置
     * @param string $configId
     * @param string $newConfigName
     * @param string $createdBy
     * @return array
     */
    public function copyConfig($configId, $newConfigName, $createdBy = '')
    {
        try {
            $originalConfig = PosterTemplateConfig::where('id', $configId)->find();
            
            if (!$originalConfig) {
                throw new \Exception('Original config not found');
            }
            
            $newConfigData = [
                'template_id' => $originalConfig->template_id,
                'template_title' => $originalConfig->template_title,
                'config_name' => $newConfigName,
                'config_description' => $originalConfig->config_description . ' (复制)',
                'parameters' => $originalConfig->parameters,
                'created_by' => $createdBy,
                'status' => PosterTemplateConfig::STATUS_ENABLED
            ];
            
            $newConfig = PosterTemplateConfig::createConfig($newConfigData);
            
            if (!$newConfig) {
                throw new \Exception('Failed to create copied config');
            }
            
            Log::info('Template config copied', [
                'original_config_id' => $configId,
                'new_config_id' => $newConfig->id,
                'created_by' => $createdBy
            ]);
            
            return [
                'success' => true,
                'data' => [
                    'configId' => $newConfig->id,
                    'configName' => $newConfigName
                ]
            ];
            
        } catch (\Exception $e) {
            Log::error('Copy config failed', [
                'config_id' => $configId,
                'new_config_name' => $newConfigName,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 只解析模板参数，不创建配置
     * @param string $templateId
     * @return array
     */
    public function parseTemplateOnly($templateId)
    {
        try {
            // 调用API解析模板
            $parseResponse = $this->apiClient->parseTemplate($templateId);

            if (!$parseResponse['success']) {
                throw new \Exception($parseResponse['error'] ?? 'Failed to parse template');
            }

            $parseData = $parseResponse['data'];

            // 格式化参数数据 - 根据API文档，字段名是parameterCandidates
            $parameters = [];
            $parameterCandidates = $parseData['parameterCandidates'] ?? $parseData['parameters'] ?? [];

            if (!empty($parameterCandidates) && is_array($parameterCandidates)) {
                foreach ($parameterCandidates as $param) {
                    $parameters[] = [
                        'name' => $param['suggestedName'] ?? $param['name'] ?? '',
                        'type' => $param['suggestedType'] ?? $param['type'] ?? 'text',
                        'description' => $param['suggestedDescription'] ?? $param['description'] ?? '',
                        'required' => $param['isRequired'] ?? $param['required'] ?? false,
                        'default_value' => $param['originalText'] ?? $param['default_value'] ?? '',
                        'options' => $param['options'] ?? [],
                        'validation' => $param['validation'] ?? [],
                        'label' => $param['suggestedLabel'] ?? $param['label'] ?? ($param['suggestedName'] ?? $param['name'] ?? ''),
                        'element_uuid' => $param['elementUuid'] ?? '',
                        'text_category' => $param['textCategory'] ?? '',
                        'max_length' => $param['maxLength'] ?? 255
                    ];
                }
            }

            Log::info('Template parsed successfully', [
                'template_id' => $templateId,
                'parameter_count' => count($parameters),
                'template_title' => $parseData['templateTitle'] ?? $parseData['title'] ?? '',
                'has_parameter_candidates' => !empty($parameterCandidates),
                'raw_data_keys' => array_keys($parseData)
            ]);

            return [
                'success' => true,
                'data' => [
                    'template_id' => $templateId,
                    'template_title' => $parseData['templateTitle'] ?? $parseData['title'] ?? '',
                    'parameters' => $parameters,
                    'summary' => $parseData['summary'] ?? [],
                    'text_elements' => $parseData['textElements'] ?? []
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Parse template failed', [
                'template_id' => $templateId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
