<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

/**
 * 动态参数模板系统 - 后台管理控制器
 */

namespace app\admin\controller;

use app\common\service\PosterTemplateService;
use app\common\service\PosterUserDataService;
use think\facade\Log;

class PosterTemplate extends AdminBase
{
    protected $templateService;
    protected $userDataService;
    
    public function __construct()
    {
        parent::__construct();
        $this->templateService = new PosterTemplateService();
        $this->userDataService = new PosterUserDataService();
    }
    
    /**
     * 模板配置列表页面（下划线命名）
     */
    public function config_list()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();

            $filters = [];

            // 只添加非空的过滤条件
            if (!empty($get['template_id'])) {
                $filters['template_id'] = $get['template_id'];
            }
            if (!empty($get['created_by'])) {
                $filters['created_by'] = $get['created_by'];
            }
            if (isset($get['status']) && $get['status'] !== '') {
                $filters['status'] = $get['status'];
            }
            if (!empty($get['keyword'])) {
                $filters['keyword'] = $get['keyword'];
            }



            $page = intval($get['page'] ?? 1);
            $pageSize = intval($get['limit'] ?? 15);

            // 确保分页参数有效
            if ($page < 1) $page = 1;
            if ($pageSize < 1) $pageSize = 15;
            if ($pageSize > 100) $pageSize = 100; // 限制最大页面大小

            $result = $this->templateService->getConfigList($filters, $page, $pageSize);

            if ($result['success']) {
                $this->_success('', $result['data']);
            } else {
                $this->_error($result['error']);
            }
        }

        return $this->fetch();
    }

    /**
     * 模板配置列表页面（驼峰命名 - 备用）
     */
    public function configList()
    {
        return $this->config_list();
    }

    /**
     * 添加模板配置页面（下划线命名）
     */
    public function config_add()
    {
        return $this->configAdd();
    }

    /**
     * 编辑模板配置页面（下划线命名）
     */
    public function config_edit($id = '')
    {
        return $this->configEdit($id);
    }

    /**
     * 删除模板配置（下划线命名）
     */
    public function config_delete($id = '')
    {
        return $this->configDelete($id);
    }
    
    /**
     * 添加模板配置页面
     */
    public function configAdd()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();

            $result = $this->validate($post, [
                'template_id' => 'require',
                'config_name' => 'require|max:255',
                'config_description' => 'max:1000'
            ]);

            if ($result !== true) {
                $this->_error($result);
            }

            $result = $this->templateService->parseAndCreateConfig(
                $post['template_id'],
                $post['config_name'],
                $post['config_description'] ?? '',
                $this->admin_id
            );

            if ($result['success']) {
                $this->_success('配置创建成功！', $result['data']);
            } else {
                $this->_error($result['error']);
            }
        }

        return $this->fetch();
    }
    
    /**
     * 编辑模板配置页面
     */
    public function configEdit($id)
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            
            $result = $this->validate($post, [
                'config_name' => 'require|max:255',
                'config_description' => 'max:1000',
                'parameters' => 'require|array'
            ]);
            
            if ($result !== true) {
                $this->_error($result);
            }
            
            $updateData = [
                'config_name' => $post['config_name'],
                'config_description' => $post['config_description'] ?? '',
                'parameters' => $post['parameters']
            ];
            
            $result = $this->templateService->updateConfig($id, $updateData);
            
            if ($result['success']) {
                $this->_success('配置更新成功！');
            } else {
                $this->_error($result['error']);
            }
        }
        
        // 获取配置详情
        $result = $this->templateService->getConfigDetail($id);
        if (!$result['success']) {
            $this->error($result['error']);
        }
        
        $this->assign('config', $result['data']);
        return $this->fetch();
    }
    
    /**
     * 删除模板配置
     */
    public function configDelete($id)
    {
        $result = $this->templateService->deleteConfig($id);
        
        if ($result['success']) {
            $this->_success('配置删除成功！');
        } else {
            $this->_error($result['error']);
        }
    }
    
    /**
     * 复制模板配置
     */
    public function configCopy()
    {
        $post = $this->request->post();
        
        $result = $this->validate($post, [
            'config_id' => 'require',
            'new_config_name' => 'require|max:255'
        ]);
        
        if ($result !== true) {
            $this->_error($result);
        }
        
        $result = $this->templateService->copyConfig(
            $post['config_id'],
            $post['new_config_name'],
            $this->admin_id
        );
        
        if ($result['success']) {
            $this->_success('配置复制成功！', $result['data']);
        } else {
            $this->_error($result['error']);
        }
    }
    
    /**
     * 获取模板列表（下划线命名）
     */
    public function get_templates()
    {
        return $this->getTemplates();
    }

    /**
     * 获取模板列表（AJAX）
     */
    public function getTemplates()
    {
        $get = $this->request->get();
        
        $params = [
            'page' => $get['page'] ?? 1,
            'pageSize' => $get['pageSize'] ?? 12,
            'category' => $get['category'] ?? '',
            'keyword' => $get['keyword'] ?? ''
        ];
        
        $result = $this->templateService->getTemplateList($params);
        
        if ($result['success']) {
            $this->_success('', $result['data']);
        } else {
            $this->_error($result['error']);
        }
    }
    
    /**
     * 解析模板（AJAX）
     */
    public function parseTemplate()
    {
        $post = $this->request->post();

        if (empty($post['template_id'])) {
            $this->_error('模板ID不能为空');
        }

        // 只解析模板，不创建配置
        $result = $this->templateService->parseTemplateOnly($post['template_id']);

        if ($result['success']) {
            $this->_success('模板解析成功！', $result['data']['parameters']);
        } else {
            $this->_error($result['error']);
        }
    }


    
    /**
     * 用户数据列表页面（下划线命名）
     */
    public function user_data_list()
    {
        return $this->userDataList();
    }

    /**
     * 用户数据列表页面
     */
    public function userDataList()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            
            $filters = [
                'config_id' => $get['config_id'] ?? '',
                'user_id' => $get['user_id'] ?? '',
                'is_draft' => isset($get['is_draft']) ? (bool)$get['is_draft'] : null
            ];
            
            $page = $get['page'] ?? 1;
            $pageSize = $get['limit'] ?? $this->page_size;
            
            $result = $this->userDataService->getUserDataList($filters, $page, $pageSize);
            
            if ($result['success']) {
                $this->_success('', $result['data']);
            } else {
                $this->_error($result['error']);
            }
        }

        // 获取配置列表用于筛选
        $configResult = $this->templateService->getConfigList([], 1, 100);
        $configs = $configResult['success'] ? $configResult['data']['list'] : [];

        $this->assign('configs', $configs);
        return $this->fetch();
    }
    
    /**
     * 生成预览（AJAX）
     */
    public function generatePreview()
    {
        $post = $this->request->post();
        
        if (empty($post['data_id'])) {
            $this->_error('数据ID不能为空');
        }
        
        $result = $this->userDataService->generatePreview($post['data_id']);
        
        if ($result['success']) {
            $this->_success('预览生成成功！', $result['data']);
        } else {
            $this->_error($result['error']);
        }
    }
    
    /**
     * 生成图片（AJAX）
     */
    public function generateImage()
    {
        $post = $this->request->post();
        
        if (empty($post['data_id'])) {
            $this->_error('数据ID不能为空');
        }
        
        $options = [
            'width' => $post['width'] ?? 1242,
            'height' => $post['height'] ?? 2208,
            'quality' => $post['quality'] ?? 0.9,
            'type' => $post['type'] ?? 'file'
        ];
        
        $result = $this->userDataService->generateImage($post['data_id'], $options);
        
        if ($result['success']) {
            $this->_success('图片生成成功！', $result['data']);
        } else {
            $this->_error($result['error']);
        }
    }
    
    /**
     * 统计数据（AJAX）
     */
    public function getStats()
    {
        try {
            $stats = [
                'total_configs' => \app\common\model\PosterTemplateConfig::where('status', 1)->count(),
                'total_user_data' => \app\common\model\PosterUserData::count(),
                'total_generations' => \app\common\model\PosterGenerationRecord::count(),
                'success_rate' => 0
            ];
            
            $generationStats = \app\common\model\PosterGenerationRecord::getGenerationStats();
            $stats['success_rate'] = $generationStats['success_rate'];
            $stats['avg_generation_time'] = $generationStats['avg_generation_time'];
            
            $this->_success('', $stats);
            
        } catch (\Exception $e) {
            $this->_error('获取统计数据失败：' . $e->getMessage());
        }
    }

    /**
     * 用户数据删除
     */
    public function user_data_delete()
    {
        $post = $this->request->post();

        if (empty($post['id'])) {
            $this->_error('数据ID不能为空');
        }

        $result = $this->userDataService->deleteUserData($post['id']);

        if ($result['success']) {
            $this->_success('删除成功！');
        } else {
            $this->_error($result['error']);
        }
    }

    /**
     * 批量生成图片
     */
    public function batch_generate()
    {
        $post = $this->request->post();

        if (empty($post['ids']) || !is_array($post['ids'])) {
            $this->_error('请选择要生成的数据');
        }

        $successCount = 0;
        $failCount = 0;
        $errors = [];

        foreach ($post['ids'] as $id) {
            $result = $this->userDataService->generateImage($id);
            if ($result['success']) {
                $successCount++;
            } else {
                $failCount++;
                $errors[] = "ID {$id}: " . $result['error'];
            }
        }

        $message = "批量生成完成：成功 {$successCount} 个，失败 {$failCount} 个";
        if (!empty($errors)) {
            $message .= "。错误详情：" . implode('; ', array_slice($errors, 0, 3));
        }

        $this->_success($message);
    }

    /**
     * 批量删除用户数据
     */
    public function batch_delete()
    {
        $post = $this->request->post();

        if (empty($post['ids']) || !is_array($post['ids'])) {
            $this->_error('请选择要删除的数据');
        }

        $successCount = 0;
        $failCount = 0;

        foreach ($post['ids'] as $id) {
            $result = $this->userDataService->deleteUserData($id);
            if ($result['success']) {
                $successCount++;
            } else {
                $failCount++;
            }
        }

        $message = "批量删除完成：成功 {$successCount} 个，失败 {$failCount} 个";
        $this->_success($message);
    }

    /**
     * 生成记录页面
     */
    public function generation_records()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();

            $filters = [
                'status' => $get['status'] ?? '',
                'data_id' => $get['data_id'] ?? '',
                'image_format' => $get['image_format'] ?? '',
                'date_range' => $get['date_range'] ?? ''
            ];

            $page = $get['page'] ?? 1;
            $pageSize = $get['limit'] ?? $this->page_size;

            $result = $this->getGenerationRecords($filters, $page, $pageSize);

            if ($result['success']) {
                $this->_success('', $result['data']);
            } else {
                $this->_error($result['error']);
            }
        }

        return $this->fetch();
    }

    /**
     * 统计页面
     */
    public function statistics()
    {
        return $this->fetch();
    }

    /**
     * 删除生成记录
     */
    public function delete_generation_record()
    {
        $post = $this->request->post();

        if (empty($post['id'])) {
            $this->_error('记录ID不能为空');
        }

        try {
            \app\common\model\PosterGenerationRecord::where('id', $post['id'])->delete();
            $this->_success('删除成功！');
        } catch (\Exception $e) {
            $this->_error('删除失败：' . $e->getMessage());
        }
    }

    /**
     * 批量删除生成记录
     */
    public function batch_delete_records()
    {
        $post = $this->request->post();

        if (empty($post['ids']) || !is_array($post['ids'])) {
            $this->_error('请选择要删除的记录');
        }

        try {
            \app\common\model\PosterGenerationRecord::where('id', 'in', $post['ids'])->delete();
            $this->_success('批量删除成功！');
        } catch (\Exception $e) {
            $this->_error('批量删除失败：' . $e->getMessage());
        }
    }

    /**
     * 获取生成记录列表
     */
    private function getGenerationRecords($filters = [], $page = 1, $pageSize = 15)
    {
        try {
            $where = [];

            if (!empty($filters['status'])) {
                $where[] = ['status', '=', $filters['status']];
            }

            if (!empty($filters['data_id'])) {
                $where[] = ['data_id', 'like', '%' . $filters['data_id'] . '%'];
            }

            if (!empty($filters['image_format'])) {
                $where[] = ['image_format', '=', $filters['image_format']];
            }

            if (!empty($filters['date_range'])) {
                $dates = explode(' - ', $filters['date_range']);
                if (count($dates) == 2) {
                    $where[] = ['created_at', 'between', [
                        strtotime($dates[0] . ' 00:00:00'),
                        strtotime($dates[1] . ' 23:59:59')
                    ]];
                }
            }

            $total = \app\common\model\PosterGenerationRecord::where($where)->count();

            $list = \app\common\model\PosterGenerationRecord::where($where)
                ->order('created_at', 'desc')
                ->page($page, $pageSize)
                ->select()
                ->toArray();

            // 格式化数据
            foreach ($list as &$item) {
                $item['created_at'] = date('Y-m-d H:i:s', $item['created_at']);
                $item['file_size_formatted'] = $this->formatFileSize($item['file_size']);
            }

            return [
                'success' => true,
                'data' => [
                    'list' => $list,
                    'total' => $total,
                    'page' => $page,
                    'pageSize' => $pageSize
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 格式化文件大小
     */
    private function formatFileSize($bytes)
    {
        if (!$bytes) return '-';

        $units = ['B', 'KB', 'MB', 'GB'];
        $i = 0;

        while ($bytes >= 1024 && $i < count($units) - 1) {
            $bytes /= 1024;
            $i++;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * 模板浏览器页面
     */
    public function template_browser()
    {
        return $this->fetch();
    }

    /**
     * 模板浏览器页面（驼峰命名）
     */
    public function templateBrowser()
    {
        return $this->template_browser();
    }
}
