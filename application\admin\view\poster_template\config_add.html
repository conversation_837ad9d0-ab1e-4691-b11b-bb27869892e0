{layout name="layout1" /}
<div class="layui-fluid">
<div class="layui-card">
    <div class="layui-card-header">
        <span>添加模板配置</span>
        <div class="layui-btn-group fr">
            <a class="layui-btn layui-btn-primary layui-btn-sm" href="{:url('poster_template/config_list')}">
                <i class="layui-icon layui-icon-return"></i>返回列表
            </a>
        </div>
    </div>
    <div class="layui-card-body">
        <form class="layui-form" lay-filter="config-form">
            <div class="layui-row layui-col-space20">
                <!-- 左侧：模板选择 -->
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">选择模板</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item">
                                <label class="layui-form-label">搜索模板</label>
                                <div class="layui-input-block">
                                    <input type="text" id="template-search" placeholder="输入关键词搜索模板" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">模板分类</label>
                                <div class="layui-input-block">
                                    <select id="template-category">
                                        <option value="">全部分类</option>
                                        <option value="poster">海报</option>
                                        <option value="product">商品</option>
                                        <option value="social">社交</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- 模板列表 -->
                            <div id="template-list" class="template-list">
                                <div class="loading-placeholder">
                                    <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
                                    正在加载模板...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：配置信息 -->
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">配置信息</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item">
                                <label class="layui-form-label">模板ID</label>
                                <div class="layui-input-block">
                                    <input type="text" name="template_id" readonly placeholder="请先选择模板" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">配置名称</label>
                                <div class="layui-input-block">
                                    <input type="text" name="config_name" required lay-verify="required" placeholder="请输入配置名称" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">配置描述</label>
                                <div class="layui-input-block">
                                    <textarea name="config_description" placeholder="请输入配置描述" class="layui-textarea"></textarea>
                                </div>
                            </div>
                            
                            <!-- 模板预览 -->
                            <div id="template-preview" class="template-preview" style="display: none;">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">模板预览</label>
                                    <div class="layui-input-block">
                                        <div class="preview-container">
                                            <img id="preview-image" src="" alt="模板预览">
                                            <div class="preview-info">
                                                <p><strong>标题：</strong><span id="preview-title"></span></p>
                                                <p><strong>尺寸：</strong><span id="preview-size"></span></p>
                                                <p><strong>文本元素：</strong><span id="preview-elements"></span></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="submit" disabled>
                                        <i class="layui-icon layui-icon-add-1"></i>创建配置
                                    </button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 模板项模板 -->
<script type="text/html" id="template-item-tpl">
    {{# if(d.data && d.data.length > 0) { }}
        {{# layui.each(d.data, function(index, item){ }}
        <div class="template-item" data-id="{{item.id}}" data-title="{{item.title}}" data-thumbnail="{{item.thumbnail}}" data-width="{{item.width}}" data-height="{{item.height}}" data-elements="{{item.textElementsCount}}" data-category="{{item.category}}">
            <div class="template-thumbnail">
                <img src="{{item.thumbnail}}" alt="{{item.title}}" loading="lazy">
                <div class="template-overlay">
                    <button class="layui-btn layui-btn-sm select-btn">选择模板</button>
                    <button class="layui-btn layui-btn-sm layui-btn-primary preview-btn" data-url="{{item.previewUrl}}">预览</button>
                </div>
                {{# if(item.category) { }}
                <div class="template-category">{{item.category}}</div>
                {{# } }}
            </div>
            <div class="template-info">
                <h4 title="{{item.title}}">{{item.title}}</h4>
                <div class="template-meta">
                    <span class="meta-item">
                        <i class="layui-icon layui-icon-picture"></i>
                        {{item.width}} x {{item.height}}
                    </span>
                    <span class="meta-item">
                        <i class="layui-icon layui-icon-fonts-code"></i>
                        {{item.textElementsCount}} 个文本
                    </span>
                    {{# if(item.imageElementsCount) { }}
                    <span class="meta-item">
                        <i class="layui-icon layui-icon-picture-fine"></i>
                        {{item.imageElementsCount}} 个图片
                    </span>
                    {{# } }}
                </div>
                {{# if(item.description) { }}
                <p class="template-desc" title="{{item.description}}">{{item.description}}</p>
                {{# } }}
                <div class="template-tags">
                    {{# if(item.tags && item.tags.length > 0) { }}
                        {{# layui.each(item.tags, function(tagIndex, tag){ }}
                        <span class="template-tag">{{tag}}</span>
                        {{# }); }}
                    {{# } }}
                </div>
            </div>
        </div>
        {{# }); }}
    {{# } else { }}
        <div class="empty-placeholder">
            <i class="layui-icon layui-icon-template-1"></i>
            <p>暂无模板数据</p>
            <p>请检查迅排设计服务是否正常运行</p>
        </div>
    {{# } }}
</script>

{/block}

{block name="script"}
<script>
layui.use(['form', 'layer', 'laytpl', 'jquery'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var laytpl = layui.laytpl;
    var $ = layui.jquery;

    var selectedTemplateId = '';
    var loadingIndex = null;
    var isAutoSelecting = false; // 防止无限循环的标志
    var hasCheckedUrlParams = false; // 防止重复检查URL参数

    // 添加页面加载调试信息
    console.log('页面脚本开始执行，当前URL:', window.location.href);

    // 监听页面卸载事件
    $(window).on('beforeunload', function() {
        console.log('页面即将卸载/刷新');
    });

    // 防止表单意外提交导致页面刷新
    $('form').on('submit', function(e) {
        console.log('表单提交事件被触发');
        // 只有在明确点击提交按钮时才允许提交
        if (!$(e.target).find('button[lay-filter="submit"]:focus').length &&
            !$(document.activeElement).is('button[lay-filter="submit"]')) {
            console.log('阻止意外的表单提交');
            e.preventDefault();
            return false;
        }
    });

    // 添加全局错误处理器
    window.addEventListener('error', function(e) {
        console.error('JavaScript错误:', e.error, '文件:', e.filename, '行号:', e.lineno);
    });
    
    // 加载模板列表
    function loadTemplates(params = {}) {
        var defaultParams = {
            page: 1,
            pageSize: 12
        };
        params = Object.assign(defaultParams, params);
        
        $.get('{:url("poster_template/get_templates")}', params, function(res) {
            if (res.code == 1) {
                // 数据字段映射和处理，与template_browser.html保持一致
                var processedData = {
                    data: res.data.list ? res.data.list.map(function(item) {
                        return {
                            id: item.id,
                            title: item.title,
                            description: item.description || '',
                            thumbnail: item.thumbnail,
                            category: item.category,
                            tags: item.tags || [],
                            width: item.width,
                            height: item.height,
                            textElementsCount: item.textElementsCount || 0,
                            imageElementsCount: item.imageElementsCount || 0,
                            previewUrl: item.previewUrl || item.thumbnail
                        };
                    }) : []
                };

                var getTpl = document.getElementById('template-item-tpl').innerHTML;
                var view = document.getElementById('template-list');
                laytpl(getTpl).render(processedData, function(html) {
                    view.innerHTML = html;
                });

                // 绑定选择事件
                bindTemplateSelect();

                console.log('模板加载完成，共', processedData.data.length, '个模板');
            } else {
                document.getElementById('template-list').innerHTML =
                    '<div class="error-placeholder">加载模板失败：' + res.msg + '</div>';
            }
        }).fail(function() {
            document.getElementById('template-list').innerHTML =
                '<div class="error-placeholder">网络错误，请重试</div>';
        });
    }
    
    // 绑定模板选择事件
    function bindTemplateSelect() {
        // 选择按钮点击事件
        $('.template-item .select-btn').off('click').on('click', function(e) {
            e.stopPropagation();

            var $item = $(this).closest('.template-item');
            var templateId = $item.data('id');
            var title = $item.data('title');
            var thumbnail = $item.data('thumbnail');
            var width = $item.data('width');
            var height = $item.data('height');
            var elements = $item.data('elements');
            var category = $item.data('category');

            // 调用选择模板的通用函数
            selectTemplateById(templateId, title, thumbnail, width, height, elements, category, $item);
        });

        // 预览按钮点击事件
        $('.template-item .preview-btn').off('click').on('click', function(e) {
            e.stopPropagation();

            var previewUrl = $(this).data('url');
            if (previewUrl) {
                layer.open({
                    type: 2,
                    title: '模板预览',
                    area: ['80%', '80%'],
                    content: previewUrl
                });
            } else {
                layer.msg('该模板暂无预览', {icon: 0});
            }
        });

        // 模板卡片点击事件（显示详情）
        $('.template-item').off('click').on('click', function(e) {
            if ($(e.target).hasClass('select-btn') || $(e.target).hasClass('preview-btn') ||
                $(e.target).closest('.select-btn').length || $(e.target).closest('.preview-btn').length) {
                return;
            }

            var templateId = $(this).data('id');
            var title = $(this).data('title');
            var thumbnail = $(this).data('thumbnail');
            var width = $(this).data('width');
            var height = $(this).data('height');
            var elements = $(this).data('elements');
            var category = $(this).data('category');

            // 显示模板详情
            var content = '<div class="template-detail" style="padding: 20px;">';
            content += '<div style="text-align: center; margin-bottom: 20px;">';
            content += '<img src="' + thumbnail + '" alt="' + title + '" style="max-width: 300px; max-height: 300px; border: 1px solid #e6e6e6; border-radius: 4px;">';
            content += '</div>';
            content += '<table class="layui-table">';
            content += '<tr><td width="80">模板名称</td><td>' + title + '</td></tr>';
            content += '<tr><td>模板ID</td><td>' + templateId + '</td></tr>';
            content += '<tr><td>分类</td><td>' + (category || '未分类') + '</td></tr>';
            content += '<tr><td>尺寸</td><td>' + width + ' x ' + height + '</td></tr>';
            content += '<tr><td>文本元素</td><td>' + elements + ' 个</td></tr>';
            content += '</table>';
            content += '</div>';

            layer.open({
                type: 1,
                title: '模板详情',
                area: ['500px', '600px'],
                content: content,
                btn: ['选择此模板', '取消'],
                yes: function(index) {
                    $('.template-item[data-id="' + templateId + '"] .select-btn').click();
                    layer.close(index);
                }
            });
        });
    }

    // 通用的选择模板函数
    function selectTemplateById(templateId, title, thumbnail, width, height, elements, category, $item) {
        // 更新选中状态
        $('.template-item').removeClass('selected');
        if ($item) {
            $item.addClass('selected');
        }

        // 更新表单
        $('input[name="template_id"]').val(templateId);
        $('input[name="config_name"]').val(title + '_配置');

        // 更新预览
        $('#preview-image').attr('src', thumbnail);
        $('#preview-title').text(title);
        $('#preview-size').text(width + ' x ' + height);
        $('#preview-elements').text(elements + ' 个');
        $('#template-preview').show();

        selectedTemplateId = templateId;

        // 显示选择成功消息，但不立即解析
        var selectMsg = layer.msg('正在解析模板参数...', {icon: 16, time: 0});

        // 解析模板参数
        parseTemplate(templateId, function(success) {
            layer.close(selectMsg);
            if (success) {
                layer.msg('模板选择成功：' + title, {icon: 1});
            } else {
                // 解析失败时不重置选择状态，只显示错误信息
                layer.msg('模板解析失败，但模板已选择。您可以稍后重试或选择其他模板。', {icon: 0, time: 5000});
            }
        });
    }

    // 解析模板参数
    function parseTemplate(templateId, callback) {
        var parseLoadingIndex = layer.load(1, {shade: [0.3, '#000']});

        $.post('{:url("poster_template/parse_template")}', {
            template_id: templateId
        }, function(res) {
            layer.close(parseLoadingIndex);

            if (res.code == 1) {
                // 显示解析结果
                showTemplateParameters(res.data);
                // 启用提交按钮
                $('button[lay-filter="submit"]').removeAttr('disabled');
                // 调用成功回调
                if (callback) callback(true);
            } else {
                // 解析失败时不清除选择状态，只显示错误信息
                var errorMsg = res.msg || '未知错误';
                console.error('Template parse error:', errorMsg);

                // 检查是否是"没有可配置参数"的错误
                if (errorMsg.includes('no configurable parameters') || errorMsg.includes('没有可配置的参数')) {
                    // 这种情况下，模板选择是成功的，只是没有参数需要配置
                    showTemplateParameters([]); // 显示空参数列表
                    $('button[lay-filter="submit"]').removeAttr('disabled');
                    layer.msg('该模板没有可配置的参数，可以直接创建配置', {icon: 1, time: 3000});
                    if (callback) callback(true);
                } else {
                    // 其他错误，显示重试选项
                    layer.confirm(errorMsg + '<br><br>是否重试解析？', {
                        icon: 2,
                        title: '解析失败',
                        btn: ['重试', '取消']
                    }, function(index) {
                        layer.close(index);
                        parseTemplate(templateId, callback); // 重试
                    }, function(index) {
                        layer.close(index);
                        if (callback) callback(false);
                    });
                }
            }
        }).fail(function(xhr, status, error) {
            layer.close(parseLoadingIndex);
            console.error('Template parse network error:', error);

            // 网络错误时也提供重试选项
            layer.confirm('网络错误，无法解析模板。是否重试？', {
                icon: 2,
                title: '网络错误',
                btn: ['重试', '取消']
            }, function(index) {
                layer.close(index);
                parseTemplate(templateId, callback); // 重试
            }, function(index) {
                layer.close(index);
                if (callback) callback(false);
            });
        });
    }

    // 显示模板参数
    function showTemplateParameters(parameters) {
        // 移除之前的参数显示
        $('.template-parameters').remove();

        if (!parameters || parameters.length === 0) {
            // 显示无参数的提示信息
            var html = '<div class="template-parameters">';
            html += '<div class="layui-form-item">';
            html += '<label class="layui-form-label">参数信息</label>';
            html += '<div class="layui-input-block">';
            html += '<div class="param-preview-container">';
            html += '<div class="param-count" style="color: #999;">该模板没有可配置的参数，可以直接创建配置</div>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
            $('#template-preview').after(html);
            return;
        }

        var html = '<div class="template-parameters">';
        html += '<div class="layui-form-item">';
        html += '<label class="layui-form-label">参数预览</label>';
        html += '<div class="layui-input-block">';
        html += '<div class="param-preview-container">';
        html += '<div class="param-count">检测到 ' + parameters.length + ' 个可配置参数：</div>';
        html += '<div class="param-list">';

        parameters.forEach(function(param, index) {
            html += '<div class="param-item">';
            html += '<div class="param-info">';
            html += '<span class="param-name">' + param.name + '</span>';
            html += '<span class="param-type layui-badge layui-bg-blue">' + param.type + '</span>';
            if (param.required) {
                html += '<span class="param-required layui-badge layui-bg-red">必填</span>';
            }
            html += '</div>';
            if (param.description) {
                html += '<div class="param-desc">' + param.description + '</div>';
            }
            html += '</div>';
        });

        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';

        // 在预览区域下方显示参数信息
        $('#template-preview').after(html);
    }

    // 清除模板选择
    function clearTemplateSelection() {
        selectedTemplateId = '';
        $('input[name="template_id"]').val('');
        $('input[name="config_name"]').val('');
        $('#template-preview').hide();
        $('.template-parameters').remove();
        $('.template-item').removeClass('selected');
        $('button[lay-filter="submit"]').attr('disabled', true);
    }

    // 清除URL参数（仅在需要时）
    function clearUrlParamsIfNeeded() {
        var urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('template_id')) {
            var newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);
            console.log('URL参数已清除');
        }
    }

    // 搜索模板
    $('#template-search').on('input', function() {
        var keyword = $(this).val();
        var category = $('#template-category').val();
        
        loadTemplates({
            keyword: keyword,
            category: category
        });
    });
    
    // 分类筛选
    $('#template-category').on('change', function() {
        var category = $(this).val();
        var keyword = $('#template-search').val();
        
        loadTemplates({
            keyword: keyword,
            category: category
        });
    });
    
    // 表单提交
    form.on('submit(submit)', function(data) {
        if (!selectedTemplateId) {
            layer.msg('请先选择模板');
            return false;
        }
        
        loadingIndex = layer.load(2, {content: '正在创建配置...'});
        
        $.post('{:url("poster_template/config_add")}', data.field, function(res) {
            layer.close(loadingIndex);
            
            if (res.code == 1) {
                layer.msg('配置创建成功', {
                    icon: 1,
                    time: 2000
                }, function() {
                    location.href = '{:url("poster_template/config_list")}';
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function() {
            layer.close(loadingIndex);
            layer.msg('网络错误，请重试', {icon: 2});
        });
        
        return false;
    });
    
    // 初始加载模板
    loadTemplates();

    // 检查URL参数，如果有template_id则自动选择
    checkUrlParams();

    // 检查URL参数函数
    function checkUrlParams() {
        // 防止重复执行
        if (hasCheckedUrlParams || isAutoSelecting) {
            console.log('已经检查过URL参数或正在自动选择中，跳过');
            return;
        }

        hasCheckedUrlParams = true; // 标记已检查过

        var urlParams = new URLSearchParams(window.location.search);
        var templateId = urlParams.get('template_id');

        if (templateId) {
            console.log('URL中的模板ID:', templateId);
            isAutoSelecting = true; // 设置标志

            // 立即清除所有URL参数，防止页面刷新时重复执行
            var newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);
            console.log('URL参数已清除');

            // 等待模板加载完成后自动选择
            var attempts = 0;
            var maxAttempts = 20; // 增加最大尝试次数

            function attemptAutoSelect() {
                attempts++;
                console.log('尝试自动选择模板，第', attempts, '次');

                // 查找匹配的模板元素
                var $targetTemplate = null;
                $('.template-item').each(function() {
                    var itemId = $(this).data('id');
                    if (itemId && (itemId == templateId || itemId.toString() === templateId)) {
                        $targetTemplate = $(this);
                        return false; // 跳出循环
                    }
                });

                if ($targetTemplate && $targetTemplate.length > 0) {
                    console.log('找到目标模板，开始自动选择');

                    // 获取模板信息
                    var actualTemplateId = $targetTemplate.data('id');
                    var templateTitle = $targetTemplate.data('title');
                    var thumbnail = $targetTemplate.data('thumbnail');
                    var width = $targetTemplate.data('width');
                    var height = $targetTemplate.data('height');
                    var elements = $targetTemplate.data('elements');
                    var category = $targetTemplate.data('category');

                    // 滚动到选中的模板
                    $('html, body').animate({
                        scrollTop: $targetTemplate.offset().top - 100
                    }, 300);

                    // 使用通用的选择函数
                    selectTemplateById(actualTemplateId, templateTitle, thumbnail, width, height, elements, category, $targetTemplate);

                    console.log('自动选择完成');
                    return; // 成功，退出

                } else if (attempts < maxAttempts) {
                    // 继续尝试
                    setTimeout(attemptAutoSelect, 300);
                } else {
                    console.log('自动选择失败，未找到模板ID:', templateId);
                    console.log('可用的模板:', $('.template-item').map(function() {
                        return $(this).data('id');
                    }).get());
                    layer.msg('未找到指定的模板，请手动选择', {icon: 0});
                    isAutoSelecting = false; // 重置标志
                }
            }

            // 延迟开始，确保模板已加载
            setTimeout(attemptAutoSelect, 500);
        }
    }
});
</script>

<style>
.template-list {
    max-height: 500px;
    overflow-y: auto;
}

.template-item {
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    margin-bottom: 10px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s;
}

.template-item:hover {
    border-color: #1E9FFF;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.template-item.selected {
    border-color: #1E9FFF;
    background-color: #f0f9ff;
}

.template-thumbnail {
    position: relative;
    text-align: center;
    margin-bottom: 10px;
}

.template-thumbnail img {
    max-width: 100%;
    max-height: 150px;
    border-radius: 4px;
}

.template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.template-item:hover .template-overlay {
    opacity: 1;
}

.template-info h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: bold;
}

.template-info p {
    margin: 0;
    font-size: 12px;
    color: #666;
}

.preview-container {
    text-align: center;
}

.preview-container img {
    max-width: 200px;
    max-height: 300px;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    margin-bottom: 10px;
}

.preview-info {
    text-align: left;
    font-size: 12px;
}

.preview-info p {
    margin: 5px 0;
}

.loading-placeholder,
.error-placeholder {
    text-align: center;
    padding: 50px 20px;
    color: #999;
}

.error-placeholder {
    color: #ff5722;
}

.template-parameters {
    margin-top: 15px;
}

.param-preview-container {
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    padding: 15px;
    background: #fafafa;
}

.param-count {
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}

.param-list {
    max-height: 200px;
    overflow-y: auto;
}

.param-item {
    background: white;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 8px;
}

.param-item:last-child {
    margin-bottom: 0;
}

.param-info {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.param-name {
    font-weight: bold;
    margin-right: 10px;
    color: #333;
}

.param-type {
    margin-right: 5px;
}

.param-required {
    margin-right: 5px;
}

.param-desc {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 新增的模板样式 */
.template-category {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(30, 159, 255, 0.9);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    z-index: 2;
}

.template-overlay {
    gap: 10px;
}

.template-overlay .layui-btn {
    padding: 5px 15px;
    font-size: 12px;
}

.template-meta {
    margin-bottom: 8px;
}

.meta-item {
    display: inline-block;
    margin-right: 12px;
    font-size: 11px;
    color: #666;
}

.meta-item i {
    margin-right: 3px;
    font-size: 12px;
}

.template-desc {
    margin: 8px 0;
    font-size: 12px;
    color: #999;
    line-height: 1.4;
    max-height: 32px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.template-tags {
    margin-top: 8px;
}

.template-tag {
    display: inline-block;
    background: #f0f0f0;
    color: #666;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    margin-right: 4px;
    margin-bottom: 4px;
}

.empty-placeholder {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.empty-placeholder i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.template-detail .detail-image {
    text-align: center;
    margin-bottom: 20px;
}

.template-detail .detail-info h3 {
    margin: 0 0 15px 0;
    color: #333;
}
</style>
</div>
