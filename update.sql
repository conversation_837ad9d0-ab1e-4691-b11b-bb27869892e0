ALTER TABLE `ls_goods`
ADD COLUMN `goods_size` VARCHAR(255) NULL DEFAULT '' COMMENT '商品尺寸' AFTER `is_selffetch`,
ADD COLUMN `price_unit` VARCHAR(50) NULL DEFAULT '' COMMENT '价格单位' AFTER `goods_size`,
ADD COLUMN `sale_start_time` INT(11) NULL DEFAULT 0 COMMENT '起售时间' AFTER `price_unit`; 

-- 在goods_comment表中添加created_by_user_id字段，用于记录创建虚拟评价的用户ID
ALTER TABLE `ls_goods_comment`
ADD COLUMN `created_by_user_id` int(11) DEFAULT NULL COMMENT '创建虚拟评价的用户ID' AFTER `virtual_data`;

-- ============================================================================
-- 动态参数模板系统数据库表创建
-- 创建时间: 2025-08-24
-- 描述: 为动态参数模板系统创建所需的数据库表
-- ============================================================================

-- 1. 创建模板参数配置表
CREATE TABLE IF NOT EXISTS `ls_poster_template_configs` (
  `id` VARCHAR(32) NOT NULL COMMENT '配置ID',
  `template_id` VARCHAR(32) NOT NULL COMMENT '迅排设计模板ID',
  `template_title` VARCHAR(255) NULL COMMENT '模板标题',
  `config_name` VARCHAR(255) NOT NULL COMMENT '配置名称',
  `config_description` TEXT NULL COMMENT '配置描述',
  `parameters` JSON NOT NULL COMMENT '参数定义JSON',
  `created_by` VARCHAR(32) NULL COMMENT '创建者ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态：1启用 0禁用',

  PRIMARY KEY (`id`),
  INDEX `idx_template_id` (`template_id`),
  INDEX `idx_created_by` (`created_by`),
  INDEX `idx_status` (`status`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模板参数配置表';

-- 2. 创建用户参数数据表
CREATE TABLE IF NOT EXISTS `ls_poster_user_data` (
  `id` VARCHAR(32) NOT NULL COMMENT '数据ID',
  `config_id` VARCHAR(32) NOT NULL COMMENT '配置ID',
  `user_id` VARCHAR(32) NULL COMMENT '用户ID',
  `session_id` VARCHAR(64) NULL COMMENT '会话ID（匿名用户）',
  `parameter_values` JSON NOT NULL COMMENT '用户填写的参数值',
  `is_draft` BOOLEAN DEFAULT TRUE COMMENT '是否为草稿',
  `preview_url` VARCHAR(500) NULL COMMENT '预览页面URL',
  `generated_image_url` VARCHAR(500) NULL COMMENT '生成的图片URL',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  PRIMARY KEY (`id`),
  INDEX `idx_config_id` (`config_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_session_id` (`session_id`),
  INDEX `idx_is_draft` (`is_draft`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_user_draft` (`user_id`, `is_draft`),
  INDEX `idx_session_draft` (`session_id`, `is_draft`),

  CONSTRAINT `fk_poster_user_data_config`
    FOREIGN KEY (`config_id`)
    REFERENCES `ls_poster_template_configs`(`id`)
    ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户参数数据表';

-- 3. 创建图片生成记录表
CREATE TABLE IF NOT EXISTS `ls_poster_generation_records` (
  `id` VARCHAR(32) NOT NULL COMMENT '记录ID',
  `data_id` VARCHAR(32) NOT NULL COMMENT '参数数据ID',
  `image_url` VARCHAR(500) NOT NULL COMMENT '生成的图片URL',
  `generation_options` JSON NULL COMMENT '生成选项（宽度、高度、质量等）',
  `generation_time` DECIMAL(10,3) NULL COMMENT '生成耗时（秒）',
  `file_size` INT NULL COMMENT '文件大小（字节）',
  `image_width` INT NULL COMMENT '图片宽度',
  `image_height` INT NULL COMMENT '图片高度',
  `image_format` VARCHAR(10) NULL COMMENT '图片格式（jpg、png等）',
  `quality` DECIMAL(3,2) NULL COMMENT '图片质量（0.1-1.0）',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态：1成功 0失败',
  `error_message` TEXT NULL COMMENT '错误信息（如果生成失败）',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

  PRIMARY KEY (`id`),
  INDEX `idx_data_id` (`data_id`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_status` (`status`),
  INDEX `idx_generation_time` (`generation_time`),
  INDEX `idx_file_size` (`file_size`),
  INDEX `idx_data_status` (`data_id`, `status`),

  CONSTRAINT `fk_poster_generation_records_data`
    FOREIGN KEY (`data_id`)
    REFERENCES `ls_poster_user_data`(`id`)
    ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片生成记录表';

-- ============================================================================
-- 动态参数模板系统菜单数据
-- 创建时间: 2025-08-24
-- 描述: 为动态参数模板系统添加后台管理菜单
-- ============================================================================

-- 获取当前最大的sort值，确保新菜单排在合适位置
SET @max_sort = (SELECT IFNULL(MAX(sort), 0) FROM ls_dev_auth WHERE pid = 0);

-- 检查并添加主菜单（避免重复插入）
SET @existing_main_menu = (SELECT id FROM ls_dev_auth WHERE name = '动态参数模板' AND pid = 0 LIMIT 1);

INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`)
SELECT 0, 1, 0, '动态参数模板', 'layui-icon-template-1', @max_sort + 10, '', 0, 0, UNIX_TIMESTAMP()
WHERE @existing_main_menu IS NULL;

-- 获取主菜单ID（现有的或新插入的）
SET @main_menu_id = COALESCE(@existing_main_menu, LAST_INSERT_ID());

-- 添加子菜单（使用INSERT IGNORE避免重复）
INSERT IGNORE INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@main_menu_id, 1, 0, '模板配置', 'layui-icon-set', 100, 'poster_template/config_list', 0, 0, UNIX_TIMESTAMP()),
(@main_menu_id, 1, 0, '用户数据', 'layui-icon-user', 90, 'poster_template/user_data_list', 0, 0, UNIX_TIMESTAMP()),
(@main_menu_id, 1, 0, '生成记录', 'layui-icon-chart', 80, 'poster_template/generation_records', 0, 0, UNIX_TIMESTAMP()),
(@main_menu_id, 1, 0, '系统统计', 'layui-icon-chart-screen', 70, 'poster_template/statistics', 0, 0, UNIX_TIMESTAMP());

-- 获取子菜单ID
SET @config_menu_id = (SELECT id FROM ls_dev_auth WHERE name = '模板配置' AND pid = @main_menu_id LIMIT 1);
SET @user_data_menu_id = (SELECT id FROM ls_dev_auth WHERE name = '用户数据' AND pid = @main_menu_id LIMIT 1);
SET @records_menu_id = (SELECT id FROM ls_dev_auth WHERE name = '生成记录' AND pid = @main_menu_id LIMIT 1);
SET @stats_menu_id = (SELECT id FROM ls_dev_auth WHERE name = '系统统计' AND pid = @main_menu_id LIMIT 1);

-- 添加模板配置相关权限
INSERT IGNORE INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@config_menu_id, 2, 0, '查看配置列表', '', 100, 'poster_template/config_list', 0, 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, 0, '添加配置', '', 90, 'poster_template/config_add', 0, 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, 0, '编辑配置', '', 80, 'poster_template/config_edit', 0, 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, 0, '删除配置', '', 70, 'poster_template/config_delete', 0, 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, 0, '复制配置', '', 60, 'poster_template/config_copy', 0, 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, 0, '获取模板列表', '', 50, 'poster_template/get_templates', 0, 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, 0, '解析模板', '', 40, 'poster_template/parse_template', 0, 0, UNIX_TIMESTAMP());

-- 添加用户数据相关权限
INSERT IGNORE INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@user_data_menu_id, 2, 0, '查看用户数据', '', 100, 'poster_template/user_data_list', 0, 0, UNIX_TIMESTAMP()),
(@user_data_menu_id, 2, 0, '编辑用户数据', '', 90, 'poster_template/user_data_edit', 0, 0, UNIX_TIMESTAMP()),
(@user_data_menu_id, 2, 0, '删除用户数据', '', 80, 'poster_template/user_data_delete', 0, 0, UNIX_TIMESTAMP()),
(@user_data_menu_id, 2, 0, '生成预览', '', 70, 'poster_template/generate_preview', 0, 0, UNIX_TIMESTAMP()),
(@user_data_menu_id, 2, 0, '生成图片', '', 60, 'poster_template/generate_image', 0, 0, UNIX_TIMESTAMP());

-- 添加生成记录相关权限
INSERT IGNORE INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@records_menu_id, 2, 0, '查看生成记录', '', 100, 'poster_template/generation_records', 0, 0, UNIX_TIMESTAMP()),
(@records_menu_id, 2, 0, '删除生成记录', '', 90, 'poster_template/delete_generation_record', 0, 0, UNIX_TIMESTAMP()),
(@records_menu_id, 2, 0, '批量删除记录', '', 80, 'poster_template/batch_delete_records', 0, 0, UNIX_TIMESTAMP());

-- 添加统计相关权限
INSERT IGNORE INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@stats_menu_id, 2, 0, '查看统计数据', '', 100, 'poster_template/statistics', 0, 0, UNIX_TIMESTAMP()),
(@stats_menu_id, 2, 0, '获取统计信息', '', 90, 'poster_template/get_stats', 0, 0, UNIX_TIMESTAMP());

-- 添加外部API权限（直接挂在主菜单下）
INSERT IGNORE INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@main_menu_id, 2, 0, '外部API-获取参数数据', '', 30, 'api/poster_external/get_parameter_data', 0, 0, UNIX_TIMESTAMP()),
(@main_menu_id, 2, 0, '外部API-获取参数配置', '', 20, 'api/poster_external/get_parameter_config', 0, 0, UNIX_TIMESTAMP()),
(@main_menu_id, 2, 0, '外部API-批量获取数据', '', 10, 'api/poster_external/batch_get_parameter_data', 0, 0, UNIX_TIMESTAMP()),
(@main_menu_id, 2, 0, '外部API-更新预览URL', '', 5, 'api/poster_external/update_preview_url', 0, 0, UNIX_TIMESTAMP()),
(@main_menu_id, 2, 0, '外部API-健康检查', '', 1, 'api/poster_external/health', 0, 0, UNIX_TIMESTAMP());

-- ============================================================================
-- 修复动态参数模板系统时间戳问题
-- 创建时间: 2025-08-25
-- 描述: 修复ls_poster_template_configs表中的无效时间戳问题
-- ============================================================================

-- 1. 修复现有数据中的无效时间戳
UPDATE `ls_poster_template_configs`
SET
    `created_at` = NOW(),
    `updated_at` = NOW()
WHERE
    `created_at` = '0000-00-00 00:00:00'
    OR `updated_at` = '0000-00-00 00:00:00'
    OR `created_at` IS NULL
    OR `updated_at` IS NULL;

-- 2. 调整表结构，确保时间戳字段有正确的默认值和约束
ALTER TABLE `ls_poster_template_configs`
MODIFY COLUMN `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
MODIFY COLUMN `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 3. 验证修复结果（可选，用于检查）
-- SELECT `id`, `config_name`, `created_at`, `updated_at`
-- FROM `ls_poster_template_configs`
-- ORDER BY `created_at` DESC;

-- 4. 检查是否还有无效时间戳（可选，用于验证）
-- SELECT COUNT(*) as invalid_count
-- FROM `ls_poster_template_configs`
-- WHERE `created_at` = '0000-00-00 00:00:00'
--    OR `updated_at` = '0000-00-00 00:00:00'
--    OR `created_at` IS NULL
--    OR `updated_at` IS NULL;