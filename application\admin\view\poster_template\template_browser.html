{layout name="layout1" /}
<div class="layui-fluid">
    <!-- 面包屑导航 -->
    <div class="layui-card">
        <div class="layui-card-body" style="padding: 10px 15px;">
            <span class="layui-breadcrumb" lay-separator=">">
                <a href="{:url('poster_template/config_list')}">模板配置</a>
                <a><cite>模板浏览器</cite></a>
            </span>
        </div>
    </div>

    <div class="layui-card">
        <div class="layui-card-header">
            <span>模板浏览器</span>
            <div class="layui-btn-group fr">
                <button class="layui-btn layui-btn-sm layui-btn-primary" onclick="history.back()">
                    <i class="layui-icon layui-icon-return"></i>返回
                </button>
                <button class="layui-btn layui-btn-sm" id="refresh-templates">
                    <i class="layui-icon layui-icon-refresh"></i>刷新
                </button>
            </div>
        </div>
        <div class="layui-card-body">
            <!-- 搜索和筛选 -->
            <form class="layui-form" lay-filter="search-form">
                <div class="layui-row layui-col-space10">
                    <div class="layui-col-md3">
                        <input type="text" name="keyword" placeholder="搜索模板名称或关键词" class="layui-input" id="template-search">
                    </div>
                    <div class="layui-col-md2">
                        <select name="category" id="template-category">
                            <option value="">全部分类</option>
                            <option value="poster">海报</option>
                            <option value="banner">横幅</option>
                            <option value="card">卡片</option>
                            <option value="social">社交媒体</option>
                            <option value="business">商务</option>
                            <option value="event">活动</option>
                        </select>
                    </div>
                    <div class="layui-col-md2">
                        <select name="sort" id="template-sort">
                            <option value="">默认排序</option>
                            <option value="newest">最新优先</option>
                            <option value="popular">热门优先</option>
                            <option value="name">名称排序</option>
                        </select>
                    </div>
                    <div class="layui-col-md2">
                        <select name="pageSize" id="page-size">
                            <option value="12">12个/页</option>
                            <option value="24">24个/页</option>
                            <option value="48">48个/页</option>
                        </select>
                    </div>
                    <div class="layui-col-md3">
                        <div class="view-mode-switch">
                            <button type="button" class="layui-btn layui-btn-sm view-mode-btn active" data-mode="grid">
                                <i class="layui-icon layui-icon-template-1"></i>网格
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-primary view-mode-btn" data-mode="list">
                                <i class="layui-icon layui-icon-list"></i>列表
                            </button>
                        </div>
                    </div>
                </div>
            </form>
            
            <!-- 模板列表 -->
            <div class="template-container">
                <div class="template-grid" id="template-list">
                    <!-- 模板项将通过JavaScript动态加载 -->
                </div>
                
                <!-- 分页 -->
                <div id="pagination-container"></div>
            </div>
        </div>
    </div>
</div>

<!-- 模板网格项模板 -->
<script type="text/html" id="template-grid-tpl">
    {{# if(d.data && d.data.length > 0) { }}
        {{# layui.each(d.data, function(index, item){ }}
        <div class="template-card" data-id="{{item.id}}" data-title="{{item.title}}" data-thumbnail="{{item.thumbnail}}" data-width="{{item.width}}" data-height="{{item.height}}" data-elements="{{item.textElementsCount || 0}}" data-category="{{item.category || ''}}" data-description="{{item.description || ''}}" data-preview-url="{{item.previewUrl || item.thumbnail}}">
            <div class="template-image">
                <img src="{{item.thumbnail}}" alt="{{item.title}}" loading="lazy">
                <div class="template-overlay">
                    <button class="layui-btn layui-btn-sm template-select-btn">选择模板</button>
                    <button class="layui-btn layui-btn-sm layui-btn-primary template-preview-btn">预览</button>
                </div>
                {{# if(item.category) { }}
                <div class="template-badge">{{item.category}}</div>
                {{# } }}
            </div>
            <div class="template-content">
                <h4 class="template-title" title="{{item.title}}">{{item.title}}</h4>
                <div class="template-meta">
                    <span class="meta-tag">
                        <i class="layui-icon layui-icon-picture"></i>
                        {{item.width}}×{{item.height}}
                    </span>
                    <span class="meta-tag">
                        <i class="layui-icon layui-icon-fonts-code"></i>
                        {{item.textElementsCount || 0}}个文本
                    </span>
                </div>
                {{# if(item.description) { }}
                <p class="template-description">{{item.description}}</p>
                {{# } }}
            </div>
        </div>
        {{# }); }}
    {{# } else { }}
        <div class="empty-state">
            <i class="layui-icon layui-icon-template-1"></i>
            <h3>暂无模板</h3>
            <p>没有找到符合条件的模板</p>
        </div>
    {{# } }}
</script>

<!-- 模板列表项模板 -->
<script type="text/html" id="template-list-tpl">
    {{# if(d.data && d.data.length > 0) { }}
        {{# layui.each(d.data, function(index, item){ }}
        <div class="template-list-item" data-id="{{item.id}}" data-title="{{item.title}}" data-thumbnail="{{item.thumbnail}}" data-width="{{item.width}}" data-height="{{item.height}}" data-elements="{{item.textElementsCount || 0}}" data-category="{{item.category || ''}}" data-description="{{item.description || ''}}" data-preview-url="{{item.previewUrl || item.thumbnail}}">
            <div class="list-item-image">
                <img src="{{item.thumbnail}}" alt="{{item.title}}">
            </div>
            <div class="list-item-content">
                <div class="list-item-header">
                    <h4 class="template-title">{{item.title}}</h4>
                    <div class="template-actions">
                        <button class="layui-btn layui-btn-xs template-select-btn">选择</button>
                        <button class="layui-btn layui-btn-xs layui-btn-primary template-preview-btn">预览</button>
                    </div>
                </div>
                <div class="template-meta">
                    <span class="meta-item">ID: {{item.id}}</span>
                    <span class="meta-item">尺寸: {{item.width}}×{{item.height}}</span>
                    <span class="meta-item">文本: {{item.textElementsCount || 0}}个</span>
                    {{# if(item.category) { }}
                    <span class="meta-item">分类: {{item.category}}</span>
                    {{# } }}
                </div>
                {{# if(item.description) { }}
                <p class="template-description">{{item.description}}</p>
                {{# } }}
            </div>
        </div>
        {{# }); }}
    {{# } else { }}
        <div class="empty-state">
            <i class="layui-icon layui-icon-template-1"></i>
            <h3>暂无模板</h3>
            <p>没有找到符合条件的模板</p>
        </div>
    {{# } }}
</script>

<script>
layui.use(['form', 'layer', 'laypage', 'laytpl', 'jquery'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var laypage = layui.laypage;
    var laytpl = layui.laytpl;
    var $ = layui.jquery;
    
    var currentPage = 1;
    var currentViewMode = 'grid';
    var currentParams = {};
    
    // 初始化
    init();
    
    function init() {
        loadTemplates();
        bindEvents();
    }
    
    function bindEvents() {
        // 搜索
        $('#template-search').on('input', debounce(function() {
            currentPage = 1;
            loadTemplates();
        }, 500));
        
        // 分类筛选
        $('#template-category').on('change', function() {
            currentPage = 1;
            loadTemplates();
        });
        
        // 排序
        $('#template-sort').on('change', function() {
            currentPage = 1;
            loadTemplates();
        });
        
        // 每页数量
        $('#page-size').on('change', function() {
            currentPage = 1;
            loadTemplates();
        });
        
        // 视图模式切换
        $('.view-mode-btn').on('click', function() {
            var mode = $(this).data('mode');
            switchViewMode(mode);
        });
        
        // 刷新
        $('#refresh-templates').on('click', function() {
            loadTemplates(true);
        });
        
        // 模板选择
        $(document).on('click', '.template-select-btn', function(e) {
            e.stopPropagation();
            var $item = $(this).closest('[data-id]');
            var templateInfo = {
                id: $item.data('id'),
                title: $item.data('title'),
                thumbnail: $item.data('thumbnail'),
                width: $item.data('width'),
                height: $item.data('height'),
                textElementsCount: $item.data('elements'),
                category: $item.data('category'),
                description: $item.data('description'),
                previewUrl: $item.data('preview-url')
            };
            selectTemplate(templateInfo);
        });

        // 模板预览
        $(document).on('click', '.template-preview-btn', function(e) {
            e.stopPropagation();
            var $item = $(this).closest('[data-id]');
            var templateInfo = {
                id: $item.data('id'),
                title: $item.data('title'),
                thumbnail: $item.data('thumbnail'),
                width: $item.data('width'),
                height: $item.data('height'),
                textElementsCount: $item.data('elements'),
                category: $item.data('category'),
                description: $item.data('description'),
                previewUrl: $item.data('preview-url')
            };
            previewTemplate(templateInfo);
        });
    }
    
    function loadTemplates(forceRefresh = false) {
        var params = {
            page: currentPage,
            pageSize: $('#page-size').val() || 12,
            keyword: $('#template-search').val(),
            category: $('#template-category').val(),
            sort: $('#template-sort').val()
        };
        
        if (forceRefresh) {
            params._t = Date.now(); // 强制刷新缓存
        }
        
        currentParams = params;
        
        // 显示加载状态
        $('#template-list').html('<div class="loading-state"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i><p>正在加载模板...</p></div>');
        var loadingIndex = layer.load(1);

        $.get('{:url("poster_template/get_templates")}', params, function(res) {
            layer.close(loadingIndex);

            if (res.code == 1) {
                renderTemplates(res.data);
                renderPagination(res.data);
            } else {
                layer.msg('加载模板失败：' + (res.msg || '未知错误'), {icon: 2});
                $('#template-list').html('<div class="error-state"><i class="layui-icon layui-icon-face-cry"></i><h3>加载失败</h3><p>' + (res.msg || '请检查网络连接或联系管理员') + '</p><button class="layui-btn layui-btn-sm" onclick="loadTemplates(true)">重试</button></div>');
            }
        }).fail(function(xhr, status, error) {
            layer.close(loadingIndex);
            var errorMsg = '网络连接失败，请检查网络后重试';
            if (xhr.status === 404) {
                errorMsg = '接口不存在，请联系技术支持';
            } else if (xhr.status === 500) {
                errorMsg = '服务器内部错误，请联系管理员';
            }
            layer.msg(errorMsg, {icon: 2});
            $('#template-list').html('<div class="error-state"><i class="layui-icon layui-icon-face-cry"></i><h3>网络错误</h3><p>' + errorMsg + '</p><button class="layui-btn layui-btn-sm" onclick="loadTemplates(true)">重试</button></div>');
        });
    }
    
    function renderTemplates(data) {
        // 数据字段映射和处理
        var processedData = {
            data: data.list ? data.list.map(function(item) {
                return {
                    id: item.id,
                    title: item.title,
                    description: item.description || '',
                    thumbnail: item.thumbnail,
                    category: item.category,
                    tags: item.tags || [],
                    width: item.width,
                    height: item.height,
                    textElementsCount: item.textElementsCount || 0,
                    imageElementsCount: item.imageElementsCount || 0,
                    previewUrl: item.previewUrl || item.thumbnail
                };
            }) : []
        };

        var tplId = currentViewMode === 'grid' ? '#template-grid-tpl' : '#template-list-tpl';
        var getTpl = document.getElementById(tplId.substring(1)).innerHTML;

        laytpl(getTpl).render(processedData, function(html) {
            $('#template-list').html(html);
        });

        // 更新容器类名
        $('.template-container').removeClass('grid-mode list-mode').addClass(currentViewMode + '-mode');

        // 保存数据用于视图切换
        currentParams.lastData = processedData;
    }
    
    function renderPagination(data) {
        var total = data.total || 0;
        var pageSize = data.pageSize || parseInt($('#page-size').val()) || 12;

        if (total > pageSize) {
            laypage.render({
                elem: 'pagination-container',
                count: total,
                limit: pageSize,
                curr: currentPage,
                layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                jump: function(obj, first) {
                    if (!first) {
                        currentPage = obj.curr;
                        loadTemplates();
                    }
                }
            });
        } else {
            $('#pagination-container').empty();
        }
    }
    
    function switchViewMode(mode) {
        currentViewMode = mode;
        $('.view-mode-btn').removeClass('active layui-btn-primary').addClass('layui-btn-primary');
        $('.view-mode-btn[data-mode="' + mode + '"]').removeClass('layui-btn-primary').addClass('active');
        
        renderTemplates(currentParams.lastData || {data: []});
    }
    
    function selectTemplate(templateInfo) {
        layer.confirm('确定选择模板 "' + templateInfo.title + '" 创建配置吗？', {
            icon: 3,
            title: '确认选择'
        }, function(index) {
            // 跳转到配置创建页面，并传递模板信息
            var url = '{:url("poster_template/config_add")}?template_id=' + templateInfo.id;
            window.location.href = url;
            layer.close(index);
        });
    }
    
    function previewTemplate(templateInfo) {
        if (templateInfo.previewUrl) {
            layer.open({
                type: 2,
                title: '模板预览 - ' + templateInfo.title,
                area: ['80%', '80%'],
                content: templateInfo.previewUrl
            });
        } else {
            layer.msg('该模板暂无预览', {icon: 0});
        }
    }
    
    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
});
</script>

<style>
/* 视图模式切换 */
.view-mode-switch {
    display: flex;
    gap: 5px;
}

.view-mode-btn.active {
    background: #1E9FFF !important;
    color: white !important;
}

/* 模板容器 */
.template-container {
    margin-top: 20px;
}

/* 网格模式 */
.grid-mode .template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.template-card {
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    transition: all 0.3s;
    cursor: pointer;
}

.template-card:hover {
    border-color: #1E9FFF;
    box-shadow: 0 4px 12px rgba(30, 159, 255, 0.15);
    transform: translateY(-2px);
}

.template-image {
    position: relative;
    height: 200px;
    background: #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.template-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: transform 0.3s;
}

.template-card:hover .template-image img {
    transform: scale(1.05);
}

.template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.3s;
}

.template-card:hover .template-overlay {
    opacity: 1;
}

.template-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(30, 159, 255, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    z-index: 2;
}

.template-content {
    padding: 15px;
}

.template-title {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: bold;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.template-meta {
    margin-bottom: 8px;
}

.meta-tag {
    display: inline-block;
    background: #f0f0f0;
    color: #666;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    margin-right: 6px;
}

.meta-tag i {
    margin-right: 3px;
}

.template-description {
    margin: 8px 0 0 0;
    font-size: 12px;
    color: #999;
    line-height: 1.4;
    max-height: 32px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* 列表模式 */
.list-mode .template-grid {
    display: block;
}

.template-list-item {
    display: flex;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    background: white;
    margin-bottom: 15px;
    overflow: hidden;
    transition: all 0.3s;
}

.template-list-item:hover {
    border-color: #1E9FFF;
    box-shadow: 0 2px 8px rgba(30, 159, 255, 0.15);
}

.list-item-image {
    width: 120px;
    height: 90px;
    background: #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.list-item-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.list-item-content {
    flex: 1;
    padding: 15px;
}

.list-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.template-actions {
    display: flex;
    gap: 5px;
}

.meta-item {
    display: inline-block;
    margin-right: 15px;
    font-size: 12px;
    color: #666;
}

/* 空状态、错误状态和加载状态 */
.empty-state, .error-state, .loading-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.empty-state i, .error-state i, .loading-state i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.loading-state i {
    color: #1E9FFF;
}

.empty-state h3, .error-state h3, .loading-state h3 {
    margin: 0 0 10px 0;
    color: #666;
}

.empty-state p, .error-state p, .loading-state p {
    margin: 0 0 15px 0;
    font-size: 14px;
}

.error-state button {
    margin-top: 10px;
}

/* 分页 */
#pagination-container {
    margin-top: 30px;
    text-align: center;
}

/* 响应式 */
@media (max-width: 768px) {
    .grid-mode .template-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 15px;
    }

    .template-list-item {
        flex-direction: column;
    }

    .list-item-image {
        width: 100%;
        height: 150px;
    }

    .list-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
</style>
