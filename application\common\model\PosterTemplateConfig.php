<?php
/**
 * 动态参数模板系统 - 模板配置模型
 */

namespace app\common\model;

use think\Model;

class PosterTemplateConfig extends Model
{
    // 表名
    protected $table = 'ls_poster_template_configs';
    
    // 主键
    protected $pk = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    protected $dateFormat = 'Y-m-d H:i:s';
    
    // JSON字段
    protected $json = ['parameters'];
    protected $jsonAssoc = true;
    
    // 状态常量
    const STATUS_DISABLED = 0; // 禁用
    const STATUS_ENABLED = 1;  // 启用
    
    /**
     * 获取状态描述
     * @param int|null $status
     * @return array|string
     */
    public static function getStatusDesc($status = null)
    {
        $data = [
            self::STATUS_DISABLED => '禁用',
            self::STATUS_ENABLED => '启用',
        ];
        
        if ($status === null) {
            return $data;
        }
        
        return $data[$status] ?? '未知';
    }
    
    /**
     * 状态获取器
     * @param $value
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        return self::getStatusDesc($data['status']);
    }
    
    /**
     * 参数获取器 - 确保返回数组
     * @param $value
     * @return array
     */
    public function getParametersAttr($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return $decoded ?: [];
        }
        return $value ?: [];
    }
    
    /**
     * 参数设置器 - 确保存储为JSON
     * @param $value
     * @return string
     */
    public function setParametersAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }
    
    /**
     * 关联用户数据
     * @return \think\model\relation\HasMany
     */
    public function userData()
    {
        return $this->hasMany(PosterUserData::class, 'config_id', 'id');
    }
    
    /**
     * 获取启用的配置列表
     * @param array $where
     * @param string $order
     * @return \think\Collection
     */
    public static function getEnabledConfigs($where = [], $order = 'created_at desc')
    {
        $where['status'] = self::STATUS_ENABLED;
        return self::where($where)->order($order)->select();
    }
    
    /**
     * 根据模板ID获取配置
     * @param string $templateId
     * @return \think\Collection
     */
    public static function getByTemplateId($templateId)
    {
        return self::where([
            'template_id' => $templateId,
            'status' => self::STATUS_ENABLED
        ])->select();
    }
    
    /**
     * 创建新配置
     * @param array $data
     * @return PosterTemplateConfig|false
     */
    public static function createConfig($data)
    {
        // 生成唯一ID
        if (empty($data['id'])) {
            $data['id'] = self::generateId();
        }

        // 验证必填字段 - 修复：parameters可以为空数组
        $required = ['template_id', 'config_name'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                \think\facade\Log::error('CreateConfig validation failed', [
                    'missing_field' => $field,
                    'data_keys' => array_keys($data)
                ]);
                return false;
            }
        }

        // parameters字段必须存在，但可以为空数组
        if (!isset($data['parameters'])) {
            $data['parameters'] = [];
        }

        // 确保parameters是数组
        if (!is_array($data['parameters'])) {
            \think\facade\Log::error('CreateConfig validation failed - parameters not array', [
                'parameters_type' => gettype($data['parameters']),
                'parameters_value' => $data['parameters']
            ]);
            return false;
        }

        // 确保时间戳正确设置
        $currentTime = date('Y-m-d H:i:s');
        if (empty($data['created_at']) || $data['created_at'] === '0000-00-00 00:00:00') {
            $data['created_at'] = $currentTime;
        }
        if (empty($data['updated_at']) || $data['updated_at'] === '0000-00-00 00:00:00') {
            $data['updated_at'] = $currentTime;
        }

        \think\facade\Log::info('Creating config with data', [
            'id' => $data['id'],
            'template_id' => $data['template_id'],
            'config_name' => $data['config_name'],
            'parameter_count' => count($data['parameters']),
            'status' => $data['status'] ?? 'not_set',
            'parameters_sample' => json_encode(array_slice($data['parameters'], 0, 2))
        ]);

        try {
            $result = self::create($data);

            if ($result) {
                \think\facade\Log::info('Config created successfully', [
                    'config_id' => $result->id,
                    'created_at' => $result->created_at
                ]);
            } else {
                \think\facade\Log::error('Config creation failed - create() returned false');
            }

            return $result;
        } catch (\Exception $e) {
            \think\facade\Log::error('Config creation exception', [
                'error' => $e->getMessage(),
                'template_id' => $data['template_id'] ?? 'unknown',
                'config_name' => $data['config_name'] ?? 'unknown'
            ]);
            return false;
        }
    }
    
    /**
     * 更新配置
     * @param string $id
     * @param array $data
     * @return bool
     */
    public static function updateConfig($id, $data)
    {
        return self::where('id', $id)->update($data) !== false;
    }
    
    /**
     * 删除配置（软删除 - 设置为禁用状态）
     * @param string $id
     * @return bool
     */
    public static function deleteConfig($id)
    {
        return self::where('id', $id)->update(['status' => self::STATUS_DISABLED]) !== false;
    }
    
    /**
     * 生成唯一ID
     * @return string
     */
    public static function generateId()
    {
        return 'config_' . date('YmdHis') . '_' . uniqid();
    }
    
    /**
     * 验证参数结构
     * @param array $parameters
     * @return bool
     */
    public static function validateParameters($parameters)
    {
        if (!is_array($parameters)) {
            return false;
        }
        
        foreach ($parameters as $param) {
            if (!isset($param['elementUuid']) || 
                !isset($param['parameterName']) || 
                !isset($param['parameterType'])) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取参数统计信息
     * @return array
     */
    public function getParameterStats()
    {
        $parameters = $this->parameters;
        $stats = [
            'total' => count($parameters),
            'enabled' => 0,
            'required' => 0,
            'types' => []
        ];
        
        foreach ($parameters as $param) {
            if (!empty($param['isEnabled'])) {
                $stats['enabled']++;
            }
            if (!empty($param['isRequired'])) {
                $stats['required']++;
            }
            
            $type = $param['parameterType'] ?? 'unknown';
            $stats['types'][$type] = ($stats['types'][$type] ?? 0) + 1;
        }
        
        return $stats;
    }
}
