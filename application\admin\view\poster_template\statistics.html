{layout name="layout1" /}
<div class="layui-fluid">
    <!-- 统计卡片 -->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="stat-card">
                        <div class="stat-icon layui-bg-blue">
                            <i class="layui-icon layui-icon-template-1"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-configs">-</h3>
                            <p>模板配置</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="stat-card">
                        <div class="stat-icon layui-bg-green">
                            <i class="layui-icon layui-icon-user"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-user-data">-</h3>
                            <p>用户数据</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="stat-card">
                        <div class="stat-icon layui-bg-orange">
                            <i class="layui-icon layui-icon-picture"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-generations">-</h3>
                            <p>生成记录</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="stat-card">
                        <div class="stat-icon layui-bg-red">
                            <i class="layui-icon layui-icon-rate"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="success-rate">-</h3>
                            <p>成功率</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 图表区域 -->
    <div class="layui-row layui-col-space15" style="margin-top: 15px;">
        <!-- 生成趋势图 -->
        <div class="layui-col-md8">
            <div class="layui-card">
                <div class="layui-card-header">
                    <span>生成趋势</span>
                    <div class="layui-btn-group fr">
                        <button class="layui-btn layui-btn-xs" data-period="7">近7天</button>
                        <button class="layui-btn layui-btn-xs layui-btn-primary" data-period="30">近30天</button>
                        <button class="layui-btn layui-btn-xs layui-btn-primary" data-period="90">近90天</button>
                    </div>
                </div>
                <div class="layui-card-body">
                    <div id="trend-chart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        
        <!-- 配置使用排行 -->
        <div class="layui-col-md4">
            <div class="layui-card">
                <div class="layui-card-header">配置使用排行</div>
                <div class="layui-card-body">
                    <div id="config-ranking" style="height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 详细统计表格 -->
    <div class="layui-row" style="margin-top: 15px;">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">配置详细统计</div>
                <div class="layui-card-body">
                    <table class="layui-hide" id="config-stats-table" lay-filter="config-stats-table"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
layui.use(['table', 'layer', 'jquery'], function(){
    var table = layui.table;
    var layer = layui.layer;
    var $ = layui.jquery;
    
    // 加载统计数据
    loadStatistics();
    
    // 渲染配置统计表格
    var tableIns = table.render({
        elem: '#config-stats-table',
        url: '{:url("poster_template/get_config_stats")}',
        cols: [[
            {field: 'config_name', title: '配置名称', width: 200},
            {field: 'template_title', title: '模板标题', width: 200},
            {field: 'user_data_count', title: '用户数据', width: 100, sort: true},
            {field: 'generation_count', title: '生成次数', width: 100, sort: true},
            {field: 'success_count', title: '成功次数', width: 100, sort: true},
            {field: 'success_rate', title: '成功率', width: 100, sort: true, templet: function(d){
                return d.success_rate + '%';
            }},
            {field: 'avg_generation_time', title: '平均耗时', width: 100, sort: true, templet: function(d){
                return d.avg_generation_time ? d.avg_generation_time + 's' : '-';
            }},
            {field: 'last_used_at', title: '最后使用', width: 160}
        ]],
        page: true,
        height: 400
    });
    
    // 加载基础统计数据
    function loadStatistics() {
        $.get('{:url("poster_template/get_stats")}', function(res) {
            if(res.code == 1) {
                var data = res.data;
                $('#total-configs').text(data.total_configs || 0);
                $('#total-user-data').text(data.total_user_data || 0);
                $('#total-generations').text(data.total_generations || 0);
                $('#success-rate').text((data.success_rate || 0) + '%');
                
                // 加载图表
                loadTrendChart(7);
                loadConfigRanking();
            }
        });
    }
    
    // 加载趋势图表
    function loadTrendChart(period) {
        // 更新按钮状态
        $('.layui-btn-group button').removeClass('layui-btn-primary').addClass('layui-btn-primary');
        $('.layui-btn-group button[data-period="' + period + '"]').removeClass('layui-btn-primary');
        
        $.get('{:url("poster_template/get_trend_data")}', {period: period}, function(res) {
            if(res.code == 1) {
                renderTrendChart(res.data);
            }
        });
    }
    
    // 加载配置排行
    function loadConfigRanking() {
        $.get('{:url("poster_template/get_config_ranking")}', function(res) {
            if(res.code == 1) {
                renderConfigRanking(res.data);
            }
        });
    }
    
    // 渲染趋势图表
    function renderTrendChart(data) {
        // 这里使用简单的HTML表格展示，实际项目中可以使用ECharts等图表库
        var html = '<div class="simple-chart">';
        html += '<div class="chart-legend">';
        html += '<span class="legend-item"><span class="legend-color" style="background: #1E9FFF;"></span>生成次数</span>';
        html += '<span class="legend-item"><span class="legend-color" style="background: #5FB878;"></span>成功次数</span>';
        html += '</div>';
        html += '<div class="chart-data">';
        
        if(data && data.length > 0) {
            var maxValue = Math.max.apply(Math, data.map(function(item) { return Math.max(item.total, item.success); }));
            
            data.forEach(function(item) {
                var totalHeight = (item.total / maxValue) * 200;
                var successHeight = (item.success / maxValue) * 200;
                
                html += '<div class="chart-bar">';
                html += '<div class="bar-container">';
                html += '<div class="bar total-bar" style="height: ' + totalHeight + 'px;" title="总数: ' + item.total + '"></div>';
                html += '<div class="bar success-bar" style="height: ' + successHeight + 'px;" title="成功: ' + item.success + '"></div>';
                html += '</div>';
                html += '<div class="bar-label">' + item.date + '</div>';
                html += '</div>';
            });
        } else {
            html += '<div class="no-data">暂无数据</div>';
        }
        
        html += '</div>';
        html += '</div>';
        
        $('#trend-chart').html(html);
    }
    
    // 渲染配置排行
    function renderConfigRanking(data) {
        var html = '<div class="ranking-list">';
        
        if(data && data.length > 0) {
            data.forEach(function(item, index) {
                var rankClass = index < 3 ? 'top-rank' : '';
                html += '<div class="ranking-item ' + rankClass + '">';
                html += '<div class="rank-number">' + (index + 1) + '</div>';
                html += '<div class="rank-info">';
                html += '<div class="config-name">' + item.config_name + '</div>';
                html += '<div class="usage-count">' + item.usage_count + ' 次使用</div>';
                html += '</div>';
                html += '<div class="rank-bar">';
                html += '<div class="bar-fill" style="width: ' + (item.usage_count / data[0].usage_count * 100) + '%"></div>';
                html += '</div>';
                html += '</div>';
            });
        } else {
            html += '<div class="no-data">暂无数据</div>';
        }
        
        html += '</div>';
        
        $('#config-ranking').html(html);
    }
    
    // 时间段切换事件
    $(document).on('click', '.layui-btn-group button', function() {
        var period = $(this).data('period');
        loadTrendChart(period);
    });
});
</script>

<style>
.stat-card {
    display: flex;
    align-items: center;
}
.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}
.stat-icon i {
    font-size: 24px;
    color: white;
}
.stat-info h3 {
    margin: 0;
    font-size: 28px;
    font-weight: bold;
    color: #333;
}
.stat-info p {
    margin: 5px 0 0 0;
    color: #666;
    font-size: 14px;
}

.simple-chart {
    padding: 20px;
}
.chart-legend {
    margin-bottom: 20px;
}
.legend-item {
    margin-right: 20px;
    font-size: 12px;
}
.legend-color {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 5px;
    vertical-align: middle;
}
.chart-data {
    display: flex;
    align-items: flex-end;
    height: 220px;
    border-bottom: 1px solid #e6e6e6;
    border-left: 1px solid #e6e6e6;
}
.chart-bar {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 2px;
}
.bar-container {
    position: relative;
    width: 100%;
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: flex-end;
}
.bar {
    width: 20px;
    margin: 0 1px;
    border-radius: 2px 2px 0 0;
}
.total-bar {
    background: #1E9FFF;
    opacity: 0.3;
}
.success-bar {
    background: #5FB878;
    position: absolute;
}
.bar-label {
    margin-top: 5px;
    font-size: 11px;
    color: #666;
    transform: rotate(-45deg);
}
.no-data {
    width: 100%;
    text-align: center;
    color: #999;
    padding: 50px 0;
}

.ranking-list {
    padding: 10px;
}
.ranking-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}
.ranking-item:last-child {
    border-bottom: none;
}
.rank-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 10px;
}
.top-rank .rank-number {
    background: #FFB800;
    color: white;
}
.rank-info {
    flex: 1;
}
.config-name {
    font-weight: bold;
    margin-bottom: 2px;
}
.usage-count {
    font-size: 12px;
    color: #666;
}
.rank-bar {
    width: 60px;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
}
.bar-fill {
    height: 100%;
    background: #1E9FFF;
    border-radius: 3px;
}
</style>
