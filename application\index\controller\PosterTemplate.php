<?php
/**
 * 动态参数模板系统 - 用户端控制器
 */

namespace app\index\controller;

use app\common\service\PosterTemplateService;
use app\common\service\PosterUserDataService;
use app\common\model\PosterTemplateConfig;
use think\Controller;
use think\facade\Session;
use think\facade\Request;

class PosterTemplate extends Controller
{
    protected $templateService;
    protected $userDataService;
    
    public function __construct()
    {
        parent::__construct();
        $this->templateService = new PosterTemplateService();
        $this->userDataService = new PosterUserDataService();
    }
    
    /**
     * 模板配置列表页面
     */
    public function index()
    {
        // 获取启用的配置列表
        $result = $this->templateService->getConfigList(['status' => 1], 1, 20);
        
        if (!$result['success']) {
            $this->error($result['error']);
        }
        
        $this->assign('configList', $result['data']['list']);
        return $this->fetch();
    }
    
    /**
     * 动态表单页面
     * @param string $configId
     */
    public function form($configId = '')
    {
        if (empty($configId)) {
            $this->error('配置ID不能为空');
        }
        
        // 获取配置详情
        $result = $this->templateService->getConfigDetail($configId);
        
        if (!$result['success']) {
            $this->error($result['error']);
        }
        
        $config = $result['data'];
        
        // 检查配置是否启用
        if ($config['status'] != 1) {
            $this->error('该配置已禁用');
        }
        
        // 获取用户ID或会话ID
        $userId = $this->getUserId();
        $sessionId = $this->getSessionId();
        
        // 查找是否有草稿数据
        $draftData = null;
        if ($userId) {
            $userDataList = \app\common\model\PosterUserData::where([
                'config_id' => $configId,
                'user_id' => $userId,
                'is_draft' => true
            ])->order('updated_at desc')->find();
            
            if ($userDataList) {
                $draftData = $userDataList->parameter_values;
            }
        } elseif ($sessionId) {
            $userDataList = \app\common\model\PosterUserData::where([
                'config_id' => $configId,
                'session_id' => $sessionId,
                'is_draft' => true
            ])->order('updated_at desc')->find();
            
            if ($userDataList) {
                $draftData = $userDataList->parameter_values;
            }
        }
        
        $this->assign([
            'config' => $config,
            'draftData' => $draftData,
            'userId' => $userId,
            'sessionId' => $sessionId
        ]);
        
        return $this->fetch();
    }
    
    /**
     * 保存用户数据（AJAX）
     */
    public function saveData()
    {
        $post = $this->request->post();
        
        // 验证必填字段
        if (empty($post['config_id']) || empty($post['parameter_values'])) {
            $this->error('参数不完整');
        }
        
        // 获取用户ID或会话ID
        $userId = $this->getUserId();
        $sessionId = $this->getSessionId();
        
        if (empty($userId) && empty($sessionId)) {
            $this->error('用户身份验证失败');
        }
        
        $data = [
            'config_id' => $post['config_id'],
            'user_id' => $userId,
            'session_id' => $sessionId,
            'parameter_values' => $post['parameter_values'],
            'is_draft' => $post['is_draft'] ?? true
        ];
        
        $result = $this->userDataService->saveUserData($data);
        
        if ($result['success']) {
            $this->success('保存成功', $result['data']);
        } else {
            $this->error($result['error']);
        }
    }
    
    /**
     * 生成预览（AJAX）
     */
    public function generatePreview()
    {
        $post = $this->request->post();
        
        if (empty($post['data_id'])) {
            $this->error('数据ID不能为空');
        }
        
        // 验证数据所有权
        if (!$this->validateDataOwnership($post['data_id'])) {
            $this->error('无权限操作此数据');
        }
        
        $result = $this->userDataService->generatePreview($post['data_id']);
        
        if ($result['success']) {
            $this->success('预览生成成功', $result['data']);
        } else {
            $this->error($result['error']);
        }
    }
    
    /**
     * 生成图片（AJAX）
     */
    public function generateImage()
    {
        $post = $this->request->post();
        
        if (empty($post['data_id'])) {
            $this->error('数据ID不能为空');
        }
        
        // 验证数据所有权
        if (!$this->validateDataOwnership($post['data_id'])) {
            $this->error('无权限操作此数据');
        }
        
        $options = [
            'width' => $post['width'] ?? 1242,
            'height' => $post['height'] ?? 2208,
            'quality' => $post['quality'] ?? 0.9,
            'type' => $post['type'] ?? 'file'
        ];
        
        $result = $this->userDataService->generateImage($post['data_id'], $options);
        
        if ($result['success']) {
            $this->success('图片生成成功', $result['data']);
        } else {
            $this->error($result['error']);
        }
    }
    
    /**
     * 获取用户数据列表（AJAX）
     */
    public function getUserDataList()
    {
        $get = $this->request->get();
        
        $userId = $this->getUserId();
        $sessionId = $this->getSessionId();
        
        if (empty($userId) && empty($sessionId)) {
            $this->error('用户身份验证失败');
        }
        
        $filters = [
            'user_id' => $userId,
            'session_id' => $sessionId,
            'is_draft' => $get['is_draft'] ?? null
        ];
        
        $page = $get['page'] ?? 1;
        $pageSize = $get['pageSize'] ?? 10;
        
        $result = $this->userDataService->getUserDataList($filters, $page, $pageSize);
        
        if ($result['success']) {
            $this->success('', $result['data']);
        } else {
            $this->error($result['error']);
        }
    }
    
    /**
     * 删除用户数据（AJAX）
     */
    public function deleteData()
    {
        $post = $this->request->post();
        
        if (empty($post['data_id'])) {
            $this->error('数据ID不能为空');
        }
        
        // 验证数据所有权
        if (!$this->validateDataOwnership($post['data_id'])) {
            $this->error('无权限操作此数据');
        }
        
        $result = \app\common\model\PosterUserData::deleteData($post['data_id']);
        
        if ($result) {
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }
    
    /**
     * 我的作品页面
     */
    public function myWorks()
    {
        $userId = $this->getUserId();
        $sessionId = $this->getSessionId();
        
        if (empty($userId) && empty($sessionId)) {
            $this->error('请先登录');
        }
        
        $this->assign([
            'userId' => $userId,
            'sessionId' => $sessionId
        ]);
        
        return $this->fetch();
    }
    
    /**
     * 获取用户ID
     * @return string|null
     */
    protected function getUserId()
    {
        // 这里根据实际的用户系统来获取用户ID
        // 例如从Session中获取登录用户信息
        $userInfo = Session::get('user_info');
        return $userInfo['user_id'] ?? null;
    }
    
    /**
     * 获取会话ID
     * @return string
     */
    protected function getSessionId()
    {
        $sessionId = Session::get('poster_session_id');
        if (empty($sessionId)) {
            $sessionId = 'session_' . uniqid() . '_' . time();
            Session::set('poster_session_id', $sessionId);
        }
        return $sessionId;
    }
    
    /**
     * 验证数据所有权
     * @param string $dataId
     * @return bool
     */
    protected function validateDataOwnership($dataId)
    {
        $userId = $this->getUserId();
        $sessionId = $this->getSessionId();
        
        $userData = \app\common\model\PosterUserData::where('id', $dataId)->find();
        
        if (!$userData) {
            return false;
        }
        
        // 检查是否为当前用户的数据
        if ($userId && $userData->user_id === $userId) {
            return true;
        }
        
        if ($sessionId && $userData->session_id === $sessionId) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 成功响应
     * @param string $msg
     * @param mixed $data
     */
    protected function success($msg = 'success', $data = [])
    {
        $result = [
            'code' => 1,
            'msg' => $msg,
            'data' => $data
        ];
        
        if ($this->request->isAjax()) {
            return json($result);
        } else {
            $this->assign('result', $result);
            return $this->fetch('success');
        }
    }
    
    /**
     * 错误响应
     * @param string $msg
     * @param mixed $data
     */
    protected function error($msg = 'error', $data = [])
    {
        $result = [
            'code' => 0,
            'msg' => $msg,
            'data' => $data
        ];
        
        if ($this->request->isAjax()) {
            return json($result);
        } else {
            $this->assign('result', $result);
            return $this->fetch('error');
        }
    }

    /**
     * 我的作品页面（下划线命名）
     */
    public function my_works()
    {
        return $this->myWorks();
    }
}
