<?php
/**
 * 动态参数模板系统用户端API控制器
 * 供移动端项目调用
 */

namespace app\api\controller;

use app\common\model\PosterTemplateConfig;
use app\common\model\PosterUserData;
use app\common\model\PosterGenerationRecord;
use app\common\service\PosterTemplateService;
use app\common\service\PosterUserDataService;
use think\facade\Log;
use think\facade\Validate;

class Poster extends ApiBase
{
    protected $templateService;
    protected $userDataService;
    
    public function __construct()
    {
        parent::__construct();
        $this->templateService = new PosterTemplateService();
        $this->userDataService = new PosterUserDataService();
    }
    
    /**
     * 获取配置列表
     * GET /api/poster/configs
     */
    public function getConfigs()
    {
        try {
            $get = $this->request->get();
            
            $filters = [
                'status' => 1, // 只获取启用的配置
                'keyword' => $get['keyword'] ?? '',
                'template_id' => $get['template_id'] ?? ''
            ];
            
            $page = $get['page'] ?? 1;
            $pageSize = $get['pageSize'] ?? 10;
            
            $result = $this->templateService->getConfigList($filters, $page, $pageSize);
            
            if ($result['success']) {
                // 格式化数据，只返回必要字段
                $list = array_map(function($config) {
                    return [
                        'id' => $config['id'],
                        'config_name' => $config['config_name'],
                        'config_description' => $config['config_description'],
                        'template_id' => $config['template_id'],
                        'template_title' => $config['template_title'],
                        'parameter_count' => count($config['parameters'] ?? []),
                        'created_at' => $config['created_at']
                    ];
                }, $result['data']['list']);
                
                $this->_success('获取成功', [
                    'list' => $list,
                    'total' => $result['data']['total'],
                    'page' => $page,
                    'pageSize' => $pageSize
                ]);
            } else {
                $this->_error($result['error']);
            }
            
        } catch (\Exception $e) {
            Log::error('Get configs failed', [
                'error' => $e->getMessage(),
                'user_id' => $this->user_id
            ]);
            $this->_error('获取配置列表失败');
        }
    }
    
    /**
     * 获取配置详情
     * GET /api/poster/config/{configId}
     */
    public function getConfig($configId = '')
    {
        try {
            if (empty($configId)) {
                $this->_error('配置ID不能为空');
            }
            
            $result = $this->templateService->getConfigDetail($configId);
            
            if ($result['success']) {
                $config = $result['data'];
                
                // 只返回用户端需要的字段
                $responseData = [
                    'id' => $config['id'],
                    'config_name' => $config['config_name'],
                    'config_description' => $config['config_description'],
                    'template_id' => $config['template_id'],
                    'template_title' => $config['template_title'],
                    'parameters' => array_map(function($param) {
                        return [
                            'id' => $param['id'],
                            'parameter_name' => $param['parameterName'],
                            'parameter_label' => $param['parameterLabel'] ?: $param['parameterName'],
                            'parameter_type' => $param['parameterType'],
                            'parameter_description' => $param['parameterDescription'] ?? '',
                            'is_required' => $param['isRequired'] ?? false,
                            'default_value' => $param['defaultValue'] ?? '',
                            'validation_rules' => $param['validationRules'] ?? [],
                            'display_order' => $param['displayOrder'] ?? 0
                        ];
                    }, array_filter($config['parameters'] ?? [], function($param) {
                        return $param['isEnabled'] ?? true; // 只返回启用的参数
                    }))
                ];
                
                // 按显示顺序排序
                usort($responseData['parameters'], function($a, $b) {
                    return $a['display_order'] - $b['display_order'];
                });
                
                $this->_success('获取成功', $responseData);
            } else {
                $this->_error($result['error']);
            }
            
        } catch (\Exception $e) {
            Log::error('Get config detail failed', [
                'config_id' => $configId,
                'error' => $e->getMessage(),
                'user_id' => $this->user_id
            ]);
            $this->_error('获取配置详情失败');
        }
    }
    
    /**
     * 保存用户数据
     * POST /api/poster/user-data
     */
    public function saveUserData()
    {
        try {
            $post = $this->request->post();
            
            // 验证必要参数
            $validate = Validate::make([
                'config_id' => 'require',
                'parameter_values' => 'require|array'
            ]);
            
            if (!$validate->check($post)) {
                $this->_error($validate->getError());
            }
            
            // 构建用户数据
            $userData = [
                'config_id' => $post['config_id'],
                'user_id' => $this->user_id ?: null,
                'session_id' => $this->user_id ? null : session_id(),
                'parameter_values' => $post['parameter_values'],
                'is_draft' => $post['is_draft'] ?? true
            ];
            
            $result = $this->userDataService->saveUserData($userData);
            
            if ($result['success']) {
                $this->_success('保存成功', [
                    'data_id' => $result['data']['id']
                ]);
            } else {
                $this->_error($result['error']);
            }
            
        } catch (\Exception $e) {
            Log::error('Save user data failed', [
                'error' => $e->getMessage(),
                'user_id' => $this->user_id,
                'post_data' => $this->request->post()
            ]);
            $this->_error('保存用户数据失败');
        }
    }
    
    /**
     * 更新用户数据
     * PUT /api/poster/user-data/{dataId}
     */
    public function updateUserData($dataId = '')
    {
        try {
            if (empty($dataId)) {
                $this->_error('数据ID不能为空');
            }
            
            $post = $this->request->post();
            
            // 验证权限：只能更新自己的数据
            $userData = PosterUserData::where('id', $dataId)->find();
            if (!$userData) {
                $this->_error('数据不存在');
            }
            
            if ($this->user_id && $userData->user_id != $this->user_id) {
                $this->_error('无权限操作此数据');
            }
            
            if (!$this->user_id && $userData->session_id != session_id()) {
                $this->_error('无权限操作此数据');
            }
            
            $updateData = [];
            if (isset($post['parameter_values'])) {
                $updateData['parameter_values'] = $post['parameter_values'];
            }
            if (isset($post['is_draft'])) {
                $updateData['is_draft'] = $post['is_draft'];
            }
            
            $result = $this->userDataService->updateUserData($dataId, $updateData);
            
            if ($result['success']) {
                $this->_success('更新成功');
            } else {
                $this->_error($result['error']);
            }
            
        } catch (\Exception $e) {
            Log::error('Update user data failed', [
                'data_id' => $dataId,
                'error' => $e->getMessage(),
                'user_id' => $this->user_id
            ]);
            $this->_error('更新用户数据失败');
        }
    }
    
    /**
     * 获取用户数据
     * GET /api/poster/user-data/{dataId}
     */
    public function getUserData($dataId = '')
    {
        try {
            if (empty($dataId)) {
                $this->_error('数据ID不能为空');
            }
            
            $userData = PosterUserData::where('id', $dataId)->with(['config'])->find();
            if (!$userData) {
                $this->_error('数据不存在');
            }
            
            // 验证权限
            if ($this->user_id && $userData->user_id != $this->user_id) {
                $this->_error('无权限访问此数据');
            }
            
            if (!$this->user_id && $userData->session_id != session_id()) {
                $this->_error('无权限访问此数据');
            }
            
            $responseData = [
                'id' => $userData->id,
                'config_id' => $userData->config_id,
                'config_name' => $userData->config->config_name ?? '',
                'parameter_values' => $userData->parameter_values,
                'is_draft' => $userData->is_draft,
                'preview_url' => $userData->preview_url,
                'generated_image_url' => $userData->generated_image_url,
                'created_at' => date('Y-m-d H:i:s', $userData->created_at),
                'updated_at' => date('Y-m-d H:i:s', $userData->updated_at)
            ];
            
            $this->_success('获取成功', $responseData);
            
        } catch (\Exception $e) {
            Log::error('Get user data failed', [
                'data_id' => $dataId,
                'error' => $e->getMessage(),
                'user_id' => $this->user_id
            ]);
            $this->_error('获取用户数据失败');
        }
    }

    /**
     * 获取我的数据列表
     * GET /api/poster/my-data
     */
    public function getMyData()
    {
        try {
            $get = $this->request->get();

            $filters = [
                'user_id' => $this->user_id,
                'session_id' => $this->user_id ? null : session_id(),
                'is_draft' => $get['is_draft'] ?? '',
                'config_id' => $get['config_id'] ?? ''
            ];

            $page = $get['page'] ?? 1;
            $pageSize = $get['pageSize'] ?? 10;

            $result = $this->userDataService->getUserDataList($filters, $page, $pageSize);

            if ($result['success']) {
                // 格式化数据
                $list = array_map(function($data) {
                    return [
                        'id' => $data['id'],
                        'config_id' => $data['config_id'],
                        'config_name' => $data['config_name'] ?? '',
                        'is_draft' => $data['is_draft'],
                        'preview_url' => $data['preview_url'],
                        'generated_image_url' => $data['generated_image_url'],
                        'created_at' => $data['created_at'],
                        'updated_at' => $data['updated_at']
                    ];
                }, $result['data']['list']);

                $this->_success('获取成功', [
                    'list' => $list,
                    'total' => $result['data']['total'],
                    'page' => $page,
                    'pageSize' => $pageSize
                ]);
            } else {
                $this->_error($result['error']);
            }

        } catch (\Exception $e) {
            Log::error('Get my data failed', [
                'error' => $e->getMessage(),
                'user_id' => $this->user_id
            ]);
            $this->_error('获取我的数据失败');
        }
    }

    /**
     * 生成预览
     * POST /api/poster/generate-preview
     */
    public function generatePreview()
    {
        try {
            $post = $this->request->post();

            if (empty($post['data_id'])) {
                $this->_error('数据ID不能为空');
            }

            // 验证权限
            $userData = PosterUserData::where('id', $post['data_id'])->find();
            if (!$userData) {
                $this->_error('数据不存在');
            }

            if ($this->user_id && $userData->user_id != $this->user_id) {
                $this->_error('无权限操作此数据');
            }

            if (!$this->user_id && $userData->session_id != session_id()) {
                $this->_error('无权限操作此数据');
            }

            $result = $this->userDataService->generatePreview($post['data_id']);

            if ($result['success']) {
                $this->_success('预览生成成功', [
                    'preview_url' => $result['data']['preview_url']
                ]);
            } else {
                $this->_error($result['error']);
            }

        } catch (\Exception $e) {
            Log::error('Generate preview failed', [
                'error' => $e->getMessage(),
                'user_id' => $this->user_id,
                'post_data' => $this->request->post()
            ]);
            $this->_error('生成预览失败');
        }
    }

    /**
     * 生成图片
     * POST /api/poster/generate-image
     */
    public function generateImage()
    {
        try {
            $post = $this->request->post();

            if (empty($post['data_id'])) {
                $this->_error('数据ID不能为空');
            }

            // 验证权限
            $userData = PosterUserData::where('id', $post['data_id'])->find();
            if (!$userData) {
                $this->_error('数据不存在');
            }

            if ($this->user_id && $userData->user_id != $this->user_id) {
                $this->_error('无权限操作此数据');
            }

            if (!$this->user_id && $userData->session_id != session_id()) {
                $this->_error('无权限操作此数据');
            }

            // 生成选项
            $options = $post['options'] ?? [];

            $result = $this->userDataService->generateImage($post['data_id'], $options);

            if ($result['success']) {
                $this->_success('图片生成成功', [
                    'image_url' => $result['data']['image_url'],
                    'generation_id' => $result['data']['generation_id']
                ]);
            } else {
                $this->_error($result['error']);
            }

        } catch (\Exception $e) {
            Log::error('Generate image failed', [
                'error' => $e->getMessage(),
                'user_id' => $this->user_id,
                'post_data' => $this->request->post()
            ]);
            $this->_error('生成图片失败');
        }
    }

    /**
     * 删除用户数据
     * DELETE /api/poster/user-data/{dataId}
     */
    public function deleteUserData($dataId = '')
    {
        try {
            if (empty($dataId)) {
                $this->_error('数据ID不能为空');
            }

            // 验证权限
            $userData = PosterUserData::where('id', $dataId)->find();
            if (!$userData) {
                $this->_error('数据不存在');
            }

            if ($this->user_id && $userData->user_id != $this->user_id) {
                $this->_error('无权限操作此数据');
            }

            if (!$this->user_id && $userData->session_id != session_id()) {
                $this->_error('无权限操作此数据');
            }

            $result = $this->userDataService->deleteUserData($dataId);

            if ($result['success']) {
                $this->_success('删除成功');
            } else {
                $this->_error($result['error']);
            }

        } catch (\Exception $e) {
            Log::error('Delete user data failed', [
                'data_id' => $dataId,
                'error' => $e->getMessage(),
                'user_id' => $this->user_id
            ]);
            $this->_error('删除用户数据失败');
        }
    }
}
