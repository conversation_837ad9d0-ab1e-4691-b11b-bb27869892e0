{layout name="layout1" /}
<div class="layui-fluid">
    <div class="layui-card">
    <div class="layui-card-header">
        <span>用户数据管理</span>
        <div class="layui-btn-group fr">
            <button class="layui-btn layui-btn-sm" id="batch-generate-btn">
                <i class="layui-icon layui-icon-picture"></i>批量生成
            </button>
            <button class="layui-btn layui-btn-sm layui-btn-danger" id="batch-delete-btn">
                <i class="layui-icon layui-icon-delete"></i>批量删除
            </button>
        </div>
    </div>
    <div class="layui-card-body">
        <!-- 搜索表单 -->
        <form class="layui-form" lay-filter="search-form">
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md2">
                    <select name="config_id">
                        <option value="">全部配置</option>
                        {volist name="configs" id="config"}
                        <option value="{$config.id}">{$config.config_name}</option>
                        {/volist}
                    </select>
                </div>
                <div class="layui-col-md2">
                    <select name="is_draft">
                        <option value="">全部状态</option>
                        <option value="1">草稿</option>
                        <option value="0">已完成</option>
                    </select>
                </div>
                <div class="layui-col-md2">
                    <input type="text" name="user_id" placeholder="用户ID" class="layui-input">
                </div>
                <div class="layui-col-md2">
                    <input type="text" name="date_range" placeholder="创建时间" class="layui-input" id="date-range">
                </div>
                <div class="layui-col-md2">
                    <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>

        <!-- 数据表格 -->
        <table class="layui-hide" id="user-data-table" lay-filter="user-data-table"></table>
    </div>
</div>

<!-- 表格工具栏 -->
<script type="text/html" id="table-toolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="refresh">
            <i class="layui-icon layui-icon-refresh"></i>刷新
        </button>
        <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="export">
            <i class="layui-icon layui-icon-export"></i>导出
        </button>
    </div>
</script>

<!-- 行工具栏 -->
<script type="text/html" id="row-toolbar">
    <a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit">编辑</a>
    {{# if(d.preview_url) { }}
    <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="preview">预览</a>
    {{# } else { }}
    <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="generate-preview">生成预览</a>
    {{# } }}
    {{# if(!d.generated_image_url) { }}
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="generate-image">生成图片</a>
    {{# } }}
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
</script>

<!-- 状态模板 -->
<script type="text/html" id="status-tpl">
    {{# if(d.is_draft == 1) { }}
    <span class="layui-badge layui-bg-orange">草稿</span>
    {{# } else { }}
    <span class="layui-badge layui-bg-green">已完成</span>
    {{# } }}
</script>

<!-- 图片状态模板 -->
<script type="text/html" id="image-status-tpl">
    {{# if(d.generated_image_url) { }}
    <span class="layui-badge layui-bg-green">已生成</span>
    <a href="{{d.generated_image_url}}" target="_blank" class="layui-btn layui-btn-xs">查看</a>
    {{# } else { }}
    <span class="layui-badge">未生成</span>
    {{# } }}
</script>

<!-- 参数值预览模板 -->
<script type="text/html" id="params-preview-tpl">
    <div class="params-preview">
        {{# layui.each(d.parameter_values_preview, function(key, value){ }}
        <div class="param-item">
            <span class="param-key">{{key}}:</span>
            <span class="param-value">{{value}}</span>
        </div>
        {{# }); }}
    </div>
</script>

<script>
layui.use(['table', 'form', 'layer', 'laydate', 'jquery'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var laydate = layui.laydate;
    var $ = layui.jquery;

    // 日期范围选择器
    laydate.render({
        elem: '#date-range',
        type: 'date',
        range: true
    });

    // 渲染表格
    var tableIns = table.render({
        elem: '#user-data-table',
        url: '{:url("poster_template/user_data_list")}',
        toolbar: '#table-toolbar',
        defaultToolbar: ['filter', 'exports', 'print'],
        cols: [[
            {type: 'checkbox', fixed: 'left'},
            {field: 'id', title: 'ID', width: 200, fixed: 'left'},
            {field: 'config_name', title: '配置名称', width: 150},
            {field: 'user_id', title: '用户ID', width: 120},
            {field: 'is_draft', title: '状态', width: 80, templet: '#status-tpl'},
            {field: 'parameter_values_preview', title: '参数预览', width: 200, templet: '#params-preview-tpl'},
            {field: 'generated_image_url', title: '图片状态', width: 120, templet: '#image-status-tpl'},
            {field: 'created_at', title: '创建时间', width: 160},
            {field: 'updated_at', title: '更新时间', width: 160},
            {title: '操作', width: 300, toolbar: '#row-toolbar', fixed: 'right'}
        ]],
        page: true,
        height: 'full-200',
        parseData: function(res){ 
            return {
                "code": res.code,
                "msg": res.msg,
                "count": res.data.total,
                "data": res.data.list
            };
        }
    });

    // 搜索
    form.on('submit(search)', function(data){
        tableIns.reload({
            where: data.field,
            page: {curr: 1}
        });
        return false;
    });

    // 头工具栏事件
    table.on('toolbar(user-data-table)', function(obj){
        switch(obj.event){
            case 'refresh':
                tableIns.reload();
                break;
            case 'export':
                layer.msg('导出功能开发中...');
                break;
        }
    });

    // 行工具栏事件
    table.on('tool(user-data-table)', function(obj){
        var data = obj.data;

        switch(obj.event){
            case 'view':
                layer.open({
                    type: 2,
                    title: '查看用户数据',
                    area: ['80%', '80%'],
                    content: '{:url("poster_template/user_data_view")}?id=' + data.id
                });
                break;

            case 'edit':
                layer.open({
                    type: 2,
                    title: '编辑用户数据',
                    area: ['80%', '80%'],
                    content: '{:url("poster_template/user_data_edit")}?id=' + data.id
                });
                break;

            case 'preview':
                window.open(data.preview_url, '_blank');
                break;

            case 'generate-preview':
                var loadingIndex = layer.load(1, {shade: [0.3, '#000']});
                $.post('{:url("poster_template/generate_preview")}', {
                    data_id: data.id
                }, function(res){
                    layer.close(loadingIndex);
                    if(res.code == 1){
                        layer.msg('预览生成成功');
                        tableIns.reload();
                    } else {
                        layer.msg(res.msg);
                    }
                });
                break;

            case 'generate-image':
                layer.prompt({
                    title: '生成图片',
                    formType: 2,
                    value: '{"width": 800, "height": 600, "quality": 0.9}',
                    area: ['400px', '200px']
                }, function(value, index){
                    try {
                        var options = JSON.parse(value);
                        var loadingIndex = layer.load(1, {shade: [0.3, '#000']});
                        
                        $.post('{:url("poster_template/generate_image")}', {
                            data_id: data.id,
                            options: options
                        }, function(res){
                            layer.close(loadingIndex);
                            if(res.code == 1){
                                layer.msg('图片生成成功');
                                tableIns.reload();
                            } else {
                                layer.msg(res.msg);
                            }
                        });
                    } catch(e) {
                        layer.msg('选项格式错误，请输入有效的JSON');
                    }
                    layer.close(index);
                });
                break;

            case 'delete':
                layer.confirm('确定要删除此用户数据吗？删除后不可恢复！', function(index){
                    $.post('{:url("poster_template/user_data_delete")}', {
                        id: data.id
                    }, function(res){
                        if(res.code == 1){
                            layer.msg('删除成功');
                            obj.del();
                        } else {
                            layer.msg(res.msg);
                        }
                    });
                    layer.close(index);
                });
                break;
        }
    });

    // 批量操作
    $('#batch-generate-btn').click(function(){
        var checkStatus = table.checkStatus('user-data-table');
        var data = checkStatus.data;
        
        if(data.length === 0){
            layer.msg('请选择要操作的数据');
            return;
        }
        
        layer.confirm('确定要批量生成选中的 ' + data.length + ' 条数据的图片吗？', function(index){
            var ids = data.map(function(item){ return item.id; });
            var loadingIndex = layer.load(1, {shade: [0.3, '#000']});
            
            $.post('{:url("poster_template/batch_generate")}', {
                ids: ids
            }, function(res){
                layer.close(loadingIndex);
                if(res.code == 1){
                    layer.msg('批量生成任务已提交');
                    tableIns.reload();
                } else {
                    layer.msg(res.msg);
                }
            });
            layer.close(index);
        });
    });

    $('#batch-delete-btn').click(function(){
        var checkStatus = table.checkStatus('user-data-table');
        var data = checkStatus.data;
        
        if(data.length === 0){
            layer.msg('请选择要删除的数据');
            return;
        }
        
        layer.confirm('确定要删除选中的 ' + data.length + ' 条数据吗？删除后不可恢复！', function(index){
            var ids = data.map(function(item){ return item.id; });
            
            $.post('{:url("poster_template/batch_delete")}', {
                ids: ids
            }, function(res){
                if(res.code == 1){
                    layer.msg('批量删除成功');
                    tableIns.reload();
                } else {
                    layer.msg(res.msg);
                }
            });
            layer.close(index);
        });
    });
});
</script>

<style>
.params-preview {
    max-height: 100px;
    overflow-y: auto;
}
.param-item {
    margin-bottom: 2px;
    font-size: 12px;
}
.param-key {
    font-weight: bold;
    color: #666;
}
.param-value {
    color: #333;
    margin-left: 5px;
}
</style>
