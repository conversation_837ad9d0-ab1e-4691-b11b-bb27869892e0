{layout name="layout1" /}
<div class="layui-fluid">
    <div class="layui-card">
    <div class="layui-card-header">
        <span>图片生成记录</span>
        <div class="layui-btn-group fr">
            <button class="layui-btn layui-btn-sm layui-btn-danger" id="batch-delete-btn">
                <i class="layui-icon layui-icon-delete"></i>批量删除
            </button>
        </div>
    </div>
    <div class="layui-card-body">
        <!-- 搜索表单 -->
        <form class="layui-form" lay-filter="search-form">
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md2">
                    <select name="status">
                        <option value="">全部状态</option>
                        <option value="1">成功</option>
                        <option value="0">失败</option>
                    </select>
                </div>
                <div class="layui-col-md2">
                    <input type="text" name="data_id" placeholder="数据ID" class="layui-input">
                </div>
                <div class="layui-col-md2">
                    <select name="image_format">
                        <option value="">全部格式</option>
                        <option value="jpg">JPG</option>
                        <option value="png">PNG</option>
                        <option value="webp">WebP</option>
                    </select>
                </div>
                <div class="layui-col-md2">
                    <input type="text" name="date_range" placeholder="生成时间" class="layui-input" id="date-range">
                </div>
                <div class="layui-col-md2">
                    <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>

        <!-- 数据表格 -->
        <table class="layui-hide" id="records-table" lay-filter="records-table"></table>
    </div>
</div>

<!-- 表格工具栏 -->
<script type="text/html" id="table-toolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="refresh">
            <i class="layui-icon layui-icon-refresh"></i>刷新
        </button>
        <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="export">
            <i class="layui-icon layui-icon-export"></i>导出
        </button>
    </div>
</script>

<!-- 行工具栏 -->
<script type="text/html" id="row-toolbar">
    <a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
    {{# if(d.image_url) { }}
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="download">下载</a>
    {{# } }}
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
</script>

<!-- 状态模板 -->
<script type="text/html" id="status-tpl">
    {{# if(d.status == 1) { }}
    <span class="layui-badge layui-bg-green">成功</span>
    {{# } else { }}
    <span class="layui-badge layui-bg-red">失败</span>
    {{# } }}
</script>

<!-- 图片预览模板 -->
<script type="text/html" id="image-preview-tpl">
    {{# if(d.image_url) { }}
    <div class="image-preview">
        <img src="{{d.image_url}}" alt="生成图片" style="max-width: 100px; max-height: 60px; cursor: pointer;" onclick="previewImage('{{d.image_url}}')">
        <div class="image-info">
            <small>{{d.image_width}} x {{d.image_height}}</small>
            <small>{{d.file_size_formatted}}</small>
        </div>
    </div>
    {{# } else { }}
    <span class="layui-badge">无图片</span>
    {{# } }}
</script>

<!-- 生成时间模板 -->
<script type="text/html" id="generation-time-tpl">
    {{# if(d.generation_time) { }}
    <span class="{{d.generation_time > 5 ? 'layui-badge layui-bg-orange' : 'layui-badge layui-bg-green'}}">
        {{d.generation_time}}s
    </span>
    {{# } else { }}
    <span>-</span>
    {{# } }}
</script>

<script>
layui.use(['table', 'form', 'layer', 'laydate', 'jquery'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var laydate = layui.laydate;
    var $ = layui.jquery;

    // 日期范围选择器
    laydate.render({
        elem: '#date-range',
        type: 'date',
        range: true
    });

    // 渲染表格
    var tableIns = table.render({
        elem: '#records-table',
        url: '{:url("poster_template/generation_records")}',
        toolbar: '#table-toolbar',
        defaultToolbar: ['filter', 'exports', 'print'],
        cols: [[
            {type: 'checkbox', fixed: 'left'},
            {field: 'id', title: 'ID', width: 200, fixed: 'left'},
            {field: 'data_id', title: '数据ID', width: 200},
            {field: 'status', title: '状态', width: 80, templet: '#status-tpl'},
            {field: 'image_url', title: '图片预览', width: 150, templet: '#image-preview-tpl'},
            {field: 'generation_time', title: '生成耗时', width: 100, templet: '#generation-time-tpl'},
            {field: 'image_format', title: '格式', width: 80},
            {field: 'quality', title: '质量', width: 80},
            {field: 'error_message', title: '错误信息', width: 200},
            {field: 'created_at', title: '生成时间', width: 160},
            {title: '操作', width: 200, toolbar: '#row-toolbar', fixed: 'right'}
        ]],
        page: true,
        height: 'full-200',
        parseData: function(res){ 
            return {
                "code": res.code,
                "msg": res.msg,
                "count": res.data.total,
                "data": res.data.list
            };
        }
    });

    // 搜索
    form.on('submit(search)', function(data){
        tableIns.reload({
            where: data.field,
            page: {curr: 1}
        });
        return false;
    });

    // 头工具栏事件
    table.on('toolbar(records-table)', function(obj){
        switch(obj.event){
            case 'refresh':
                tableIns.reload();
                break;
            case 'export':
                layer.msg('导出功能开发中...');
                break;
        }
    });

    // 行工具栏事件
    table.on('tool(records-table)', function(obj){
        var data = obj.data;

        switch(obj.event){
            case 'view':
                layer.open({
                    type: 1,
                    title: '生成记录详情',
                    area: ['600px', '500px'],
                    content: buildRecordDetail(data)
                });
                break;

            case 'download':
                if(data.image_url) {
                    window.open(data.image_url, '_blank');
                } else {
                    layer.msg('无可下载的图片');
                }
                break;

            case 'delete':
                layer.confirm('确定要删除此生成记录吗？删除后不可恢复！', function(index){
                    $.post('{:url("poster_template/delete_generation_record")}', {
                        id: data.id
                    }, function(res){
                        if(res.code == 1){
                            layer.msg('删除成功');
                            obj.del();
                        } else {
                            layer.msg(res.msg);
                        }
                    });
                    layer.close(index);
                });
                break;
        }
    });

    // 批量删除
    $('#batch-delete-btn').click(function(){
        var checkStatus = table.checkStatus('records-table');
        var data = checkStatus.data;
        
        if(data.length === 0){
            layer.msg('请选择要删除的记录');
            return;
        }
        
        layer.confirm('确定要删除选中的 ' + data.length + ' 条记录吗？删除后不可恢复！', function(index){
            var ids = data.map(function(item){ return item.id; });
            
            $.post('{:url("poster_template/batch_delete_records")}', {
                ids: ids
            }, function(res){
                if(res.code == 1){
                    layer.msg('批量删除成功');
                    tableIns.reload();
                } else {
                    layer.msg(res.msg);
                }
            });
            layer.close(index);
        });
    });

    // 构建记录详情HTML
    function buildRecordDetail(data) {
        var html = '<div class="record-detail" style="padding: 20px;">';
        html += '<div class="layui-row layui-col-space10">';
        
        if(data.image_url) {
            html += '<div class="layui-col-md6">';
            html += '<img src="' + data.image_url + '" style="max-width: 100%; border: 1px solid #e6e6e6;">';
            html += '</div>';
        }
        
        html += '<div class="layui-col-md6">';
        html += '<table class="layui-table">';
        html += '<tr><td>记录ID</td><td>' + data.id + '</td></tr>';
        html += '<tr><td>数据ID</td><td>' + data.data_id + '</td></tr>';
        html += '<tr><td>状态</td><td>' + (data.status == 1 ? '<span class="layui-badge layui-bg-green">成功</span>' : '<span class="layui-badge layui-bg-red">失败</span>') + '</td></tr>';
        html += '<tr><td>生成耗时</td><td>' + (data.generation_time || '-') + 's</td></tr>';
        html += '<tr><td>文件大小</td><td>' + (data.file_size_formatted || '-') + '</td></tr>';
        html += '<tr><td>图片尺寸</td><td>' + (data.image_width && data.image_height ? data.image_width + ' x ' + data.image_height : '-') + '</td></tr>';
        html += '<tr><td>图片格式</td><td>' + (data.image_format || '-') + '</td></tr>';
        html += '<tr><td>图片质量</td><td>' + (data.quality || '-') + '</td></tr>';
        html += '<tr><td>生成时间</td><td>' + data.created_at + '</td></tr>';
        
        if(data.error_message) {
            html += '<tr><td>错误信息</td><td style="color: red;">' + data.error_message + '</td></tr>';
        }
        
        html += '</table>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        
        return html;
    }
});

// 图片预览函数
function previewImage(url) {
    layer.photos({
        photos: {
            data: [{
                src: url,
                alt: '生成图片'
            }]
        },
        anim: 5
    });
}
</script>

<style>
.image-preview {
    text-align: center;
}
.image-info {
    margin-top: 5px;
}
.image-info small {
    display: block;
    color: #666;
    font-size: 11px;
}
.record-detail .layui-table td {
    padding: 8px 15px;
}
</style>
